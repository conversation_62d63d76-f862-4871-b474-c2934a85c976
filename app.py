from flask import Flask, render_template, jsonify, request
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import subprocess
import threading
import os
import sys
from datetime import datetime
import deezer
from plexapi.server import PlexServer
from plexapi.exceptions import BadRequest, NotFound, Unauthorized
import json
import random
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'deemix-downloader-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables to track script execution
script_process = None
script_running = False

# Plex connection variables
plex_server = None
plex_config_file = 'config/plex_config.json'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/status')
def status():
    return jsonify({
        'running': script_running,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/search', methods=['POST'])
def search_tracks():
    """Search for tracks using Deezer API"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()

        if not query:
            return jsonify({'success': False, 'error': 'Query is required'})

        # Initialize Deezer client
        dz = deezer.Deezer()

        # Search for tracks
        search_results = dz.api.search(query, limit=10)

        if not search_results or not search_results.get('data'):
            return jsonify({'success': True, 'results': []})

        # Format results for frontend
        results = []
        for track in search_results['data']:
            results.append({
                'id': track['id'],
                'title': track['title'],
                'artist': track['artist']['name'],
                'album': track['album']['title'] if track.get('album') else 'Unknown Album'
            })

        return jsonify({'success': True, 'results': results})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/download_track', methods=['POST'])
def download_single_track():
    """Download a single track using deemix CLI"""
    try:
        data = request.get_json()
        track_url = data.get('track_url', '').strip()
        title = data.get('title', 'Unknown')
        artist = data.get('artist', 'Unknown')

        if not track_url:
            return jsonify({'success': False, 'error': 'Track URL is required'})

        # Use deemix CLI to download the track
        result = subprocess.run([
            'python3', '-m', 'deemix',
            '--portable',
            track_url
        ], capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            return jsonify({
                'success': True,
                'message': f'✅ Successfully downloaded: {title} by {artist}'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Deemix CLI failed: {result.stderr or result.stdout}'
            })

    except subprocess.TimeoutExpired:
        return jsonify({'success': False, 'error': 'Download timeout (60s)'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def run_deemix_script():
    """Run the DeemixDownloader.py script and stream output"""
    global script_process, script_running
    
    try:
        script_running = True
        socketio.emit('script_started', {'message': 'Starting DeemixDownloader.py...'})
        
        # Change to the script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Run the script with real-time output
        script_process = subprocess.Popen(
            [sys.executable, 'DeemixDownloader.py'],
            cwd=script_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Stream output line by line
        for line in iter(script_process.stdout.readline, ''):
            if line:
                socketio.emit('script_output', {'message': line.rstrip()})
        
        # Wait for process to complete
        return_code = script_process.wait()
        
        if return_code == 0:
            socketio.emit('script_completed', {
                'message': 'DeemixDownloader.py completed successfully!',
                'success': True
            })
        else:
            socketio.emit('script_completed', {
                'message': f'DeemixDownloader.py failed with exit code {return_code}',
                'success': False
            })
            
    except Exception as e:
        socketio.emit('script_error', {
            'message': f'Error running script: {str(e)}',
            'success': False
        })
    finally:
        script_running = False
        script_process = None

@socketio.on('start_download')
def handle_start_download():
    """Handle request to start the download process"""
    global script_running
    
    if script_running:
        emit('error', {'message': 'Script is already running!'})
        return
    
    # Start the script in a separate thread
    thread = threading.Thread(target=run_deemix_script)
    thread.daemon = True
    thread.start()
    
    emit('download_started', {'message': 'Download process initiated'})

@socketio.on('stop_download')
def handle_stop_download():
    """Handle request to stop the download process"""
    global script_process, script_running
    
    if not script_running or not script_process:
        emit('error', {'message': 'No script is currently running!'})
        return
    
    try:
        script_process.terminate()
        script_process.wait(timeout=5)
        emit('download_stopped', {'message': 'Download process stopped'})
    except subprocess.TimeoutExpired:
        script_process.kill()
        emit('download_stopped', {'message': 'Download process forcefully terminated'})
    except Exception as e:
        emit('error', {'message': f'Error stopping script: {str(e)}'})
    finally:
        script_running = False
        script_process = None

# Plex helper functions
def load_plex_config():
    """Load Plex configuration from file"""
    try:
        if os.path.exists(plex_config_file):
            with open(plex_config_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading Plex config: {e}")
    return {}

def save_plex_config(config):
    """Save Plex configuration to file"""
    try:
        os.makedirs(os.path.dirname(plex_config_file), exist_ok=True)
        with open(plex_config_file, 'w') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving Plex config: {e}")
        return False

def connect_to_plex(server_url, token):
    """Connect to Plex server"""
    global plex_server
    try:
        plex_server = PlexServer(server_url, token)
        return True, "Connected successfully"
    except Unauthorized:
        return False, "Invalid token or unauthorized access"
    except Exception as e:
        return False, f"Connection failed: {str(e)}"

@app.route('/plex/connect', methods=['POST'])
def plex_connect():
    """Connect to Plex server"""
    try:
        data = request.get_json()
        server_url = data.get('server_url', '').strip()
        token = data.get('token', '').strip()

        if not server_url or not token:
            return jsonify({'success': False, 'error': 'Server URL and token are required'})

        success, message = connect_to_plex(server_url, token)

        if success:
            # Save configuration for persistence
            config = {'server_url': server_url, 'token': token}
            save_plex_config(config)

            return jsonify({
                'success': True,
                'message': message,
                'server_name': plex_server.friendlyName if plex_server else 'Unknown'
            })
        else:
            return jsonify({'success': False, 'error': message})

    except Exception as e:
        return jsonify({'success': False, 'error': f'Connection error: {str(e)}'})

@app.route('/plex/status')
def plex_status():
    """Check Plex connection status"""
    global plex_server

    if plex_server:
        try:
            # Test connection
            server_name = plex_server.friendlyName
            return jsonify({
                'connected': True,
                'server_name': server_name
            })
        except:
            plex_server = None

    # Try to auto-connect using saved config
    config = load_plex_config()
    if config.get('server_url') and config.get('token'):
        success, _ = connect_to_plex(config['server_url'], config['token'])
        if success:
            return jsonify({
                'connected': True,
                'server_name': plex_server.friendlyName
            })

    return jsonify({'connected': False})

@app.route('/plex/music')
def plex_get_music():
    """Get music library from Plex"""
    global plex_server

    if not plex_server:
        return jsonify({'success': False, 'error': 'Not connected to Plex server'})

    try:
        # Get music library
        music_library = None
        for section in plex_server.library.sections():
            if section.type == 'artist':
                music_library = section
                break

        if not music_library:
            return jsonify({'success': False, 'error': 'No music library found on Plex server'})

        # Get all tracks
        tracks = []
        all_tracks = music_library.searchTracks()

        for track in all_tracks[:500]:  # Limit to first 500 tracks for performance
            try:
                tracks.append({
                    'id': track.ratingKey,
                    'title': track.title,
                    'artist': track.grandparentTitle or 'Unknown Artist',
                    'album': track.parentTitle or 'Unknown Album',
                    'duration': track.duration or 0,
                    'year': track.year or '',
                    'track_number': track.index or 0
                })
            except Exception as e:
                print(f"Error processing track: {e}")
                continue

        return jsonify({
            'success': True,
            'tracks': tracks,
            'total_count': len(all_tracks),
            'returned_count': len(tracks)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error fetching music: {str(e)}'})

@app.route('/plex/search_music', methods=['POST'])
def plex_search_music():
    """Search music library"""
    global plex_server

    if not plex_server:
        return jsonify({'success': False, 'error': 'Not connected to Plex server'})

    try:
        data = request.get_json()
        query = data.get('query', '').strip()

        if not query:
            return jsonify({'success': False, 'error': 'Search query is required'})

        # Get music library
        music_library = None
        for section in plex_server.library.sections():
            if section.type == 'artist':
                music_library = section
                break

        if not music_library:
            return jsonify({'success': False, 'error': 'No music library found'})

        # Search tracks
        search_results = music_library.searchTracks(title=query)

        tracks = []
        for track in search_results[:100]:  # Limit results
            try:
                tracks.append({
                    'id': track.ratingKey,
                    'title': track.title,
                    'artist': track.grandparentTitle or 'Unknown Artist',
                    'album': track.parentTitle or 'Unknown Album',
                    'duration': track.duration or 0,
                    'year': track.year or '',
                    'track_number': track.index or 0
                })
            except Exception as e:
                continue

        return jsonify({
            'success': True,
            'tracks': tracks,
            'query': query
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Search error: {str(e)}'})

@app.route('/plex/create_playlist', methods=['POST'])
def plex_create_playlist():
    """Create a new playlist on Plex server"""
    global plex_server

    if not plex_server:
        return jsonify({'success': False, 'error': 'Not connected to Plex server'})

    try:
        data = request.get_json()
        playlist_name = data.get('name', '').strip()
        track_ids = data.get('track_ids', [])

        if not playlist_name:
            return jsonify({'success': False, 'error': 'Playlist name is required'})

        if not track_ids:
            return jsonify({'success': False, 'error': 'At least one track must be selected'})

        # Get track objects
        tracks = []
        for track_id in track_ids:
            try:
                track = plex_server.fetchItem(track_id)
                tracks.append(track)
            except Exception as e:
                print(f"Error fetching track {track_id}: {e}")
                continue

        if not tracks:
            return jsonify({'success': False, 'error': 'No valid tracks found'})

        # Create playlist
        playlist = plex_server.createPlaylist(playlist_name, items=tracks)

        return jsonify({
            'success': True,
            'message': f'Playlist "{playlist_name}" created successfully with {len(tracks)} tracks',
            'playlist_id': playlist.ratingKey,
            'track_count': len(tracks)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error creating playlist: {str(e)}'})

# AI Playlist Suggestion Engine
PLAYLIST_SUGGESTIONS = [
    {
        "id": "classic_rock_essentials",
        "title": "Classic Rock Essentials",
        "description": "Timeless rock anthems that defined generations",
        "category": "genre",
        "tags": ["Classic Rock", "70s", "80s", "Guitar"],
        "tracks": [
            {"artist": "Led Zeppelin", "title": "Stairway to Heaven", "album": "Led Zeppelin IV"},
            {"artist": "Queen", "title": "Bohemian Rhapsody", "album": "A Night at the Opera"},
            {"artist": "Pink Floyd", "title": "Comfortably Numb", "album": "The Wall"},
            {"artist": "The Eagles", "title": "Hotel California", "album": "Hotel California"},
            {"artist": "AC/DC", "title": "Back in Black", "album": "Back in Black"},
            {"artist": "Deep Purple", "title": "Smoke on the Water", "album": "Machine Head"},
            {"artist": "Black Sabbath", "title": "Paranoid", "album": "Paranoid"},
            {"artist": "The Rolling Stones", "title": "Paint It Black", "album": "Aftermath"},
            {"artist": "Aerosmith", "title": "Dream On", "album": "Aerosmith"},
            {"artist": "Fleetwood Mac", "title": "Go Your Own Way", "album": "Rumours"},
            {"artist": "The Who", "title": "Won't Get Fooled Again", "album": "Who's Next"},
            {"artist": "Lynyrd Skynyrd", "title": "Sweet Home Alabama", "album": "Second Helping"},
            {"artist": "Boston", "title": "More Than a Feeling", "album": "Boston"},
            {"artist": "Journey", "title": "Don't Stop Believin'", "album": "Escape"},
            {"artist": "Foreigner", "title": "I Want to Know What Love Is", "album": "Agent Provocateur"}
        ]
    },
    {
        "id": "electronic_dance_hits",
        "title": "Electronic Dance Hits",
        "description": "High-energy electronic tracks to get you moving",
        "category": "genre",
        "tags": ["Electronic", "Dance", "EDM", "High Energy"],
        "tracks": [
            {"artist": "Daft Punk", "title": "One More Time", "album": "Discovery"},
            {"artist": "Calvin Harris", "title": "Feel So Close", "album": "18 Months"},
            {"artist": "Avicii", "title": "Levels", "album": "True"},
            {"artist": "Deadmau5", "title": "Ghosts 'n' Stuff", "album": "For Lack of a Better Name"},
            {"artist": "Swedish House Mafia", "title": "Don't You Worry Child", "album": "Until Now"},
            {"artist": "Skrillex", "title": "Bangarang", "album": "Bangarang"},
            {"artist": "David Guetta", "title": "Titanium", "album": "Nothing but the Beat"},
            {"artist": "Zedd", "title": "Clarity", "album": "Clarity"},
            {"artist": "Martin Garrix", "title": "Animals", "album": "Animals"},
            {"artist": "Tiësto", "title": "Adagio for Strings", "album": "Just Be"},
            {"artist": "Armin van Buuren", "title": "This Is What It Feels Like", "album": "Intense"},
            {"artist": "Above & Beyond", "title": "Sun & Moon", "album": "Group Therapy"},
            {"artist": "Porter Robinson", "title": "Language", "album": "Worlds"},
            {"artist": "Madeon", "title": "Finale", "album": "Adventure"},
            {"artist": "ODESZA", "title": "Say My Name", "album": "In Return"}
        ]
    },
    {
        "id": "jazz_standards",
        "title": "Jazz Standards",
        "description": "Essential jazz classics from the masters",
        "category": "genre",
        "tags": ["Jazz", "Standards", "Classic", "Instrumental"],
        "tracks": [
            {"artist": "Miles Davis", "title": "Kind of Blue", "album": "Kind of Blue"},
            {"artist": "John Coltrane", "title": "A Love Supreme", "album": "A Love Supreme"},
            {"artist": "Duke Ellington", "title": "Take Five", "album": "Time Out"},
            {"artist": "Billie Holiday", "title": "Strange Fruit", "album": "Lady Sings the Blues"},
            {"artist": "Ella Fitzgerald", "title": "Summertime", "album": "Porgy and Bess"},
            {"artist": "Louis Armstrong", "title": "What a Wonderful World", "album": "What a Wonderful World"},
            {"artist": "Charlie Parker", "title": "Ornithology", "album": "Bird: The Complete Charlie Parker on Verve"},
            {"artist": "Thelonious Monk", "title": "Round Midnight", "album": "Genius of Modern Music"},
            {"artist": "Art Tatum", "title": "Tea for Two", "album": "The Complete Capitol Recordings"},
            {"artist": "Dizzy Gillespie", "title": "A Night in Tunisia", "album": "The Complete RCA Victor Recordings"},
            {"artist": "Count Basie", "title": "One O'Clock Jump", "album": "The Complete Decca Recordings"},
            {"artist": "Benny Goodman", "title": "Sing, Sing, Sing", "album": "Carnegie Hall Jazz Concert"},
            {"artist": "Sarah Vaughan", "title": "Misty", "album": "Sarah Vaughan"},
            {"artist": "Nat King Cole", "title": "Unforgettable", "album": "The Nat King Cole Story"},
            {"artist": "Chet Baker", "title": "My Funny Valentine", "album": "Chet Baker Sings"}
        ]
    },
    {
        "id": "80s_new_wave",
        "title": "80s New Wave",
        "description": "Synth-driven anthems from the decade of excess",
        "category": "decade",
        "tags": ["80s", "New Wave", "Synth", "Pop"],
        "tracks": [
            {"artist": "Depeche Mode", "title": "Personal Jesus", "album": "Violator"},
            {"artist": "New Order", "title": "Blue Monday", "album": "Power, Corruption & Lies"},
            {"artist": "The Cure", "title": "Just Like Heaven", "album": "Kiss Me, Kiss Me, Kiss Me"},
            {"artist": "Duran Duran", "title": "Hungry Like the Wolf", "album": "Rio"},
            {"artist": "Tears for Fears", "title": "Everybody Wants to Rule the World", "album": "Songs from the Big Chair"},
            {"artist": "A-ha", "title": "Take On Me", "album": "Hunting High and Low"},
            {"artist": "Blondie", "title": "Heart of Glass", "album": "Parallel Lines"},
            {"artist": "Talking Heads", "title": "Burning Down the House", "album": "Speaking in Tongues"},
            {"artist": "Devo", "title": "Whip It", "album": "Freedom of Choice"},
            {"artist": "Gary Numan", "title": "Cars", "album": "The Pleasure Principle"},
            {"artist": "Kraftwerk", "title": "Computer World", "album": "Computer World"},
            {"artist": "Human League", "title": "Don't You (Forget About Me)", "album": "Dare"},
            {"artist": "Soft Cell", "title": "Tainted Love", "album": "Non-Stop Erotic Cabaret"},
            {"artist": "Eurythmics", "title": "Sweet Dreams", "album": "Sweet Dreams (Are Made of This)"},
            {"artist": "Flock of Seagulls", "title": "I Ran", "album": "A Flock of Seagulls"}
        ]
    },
    {
        "id": "90s_hip_hop",
        "title": "90s Hip-Hop",
        "description": "Golden age rap classics that shaped the culture",
        "category": "decade",
        "tags": ["90s", "Hip-Hop", "Rap", "Golden Age"],
        "tracks": [
            {"artist": "Tupac", "title": "California Love", "album": "All Eyez on Me"},
            {"artist": "The Notorious B.I.G.", "title": "Juicy", "album": "Ready to Die"},
            {"artist": "Wu-Tang Clan", "title": "C.R.E.A.M.", "album": "Enter the Wu-Tang (36 Chambers)"},
            {"artist": "Nas", "title": "N.Y. State of Mind", "album": "Illmatic"},
            {"artist": "Dr. Dre", "title": "Nuthin' but a 'G' Thang", "album": "The Chronic"},
            {"artist": "Snoop Dogg", "title": "Gin and Juice", "album": "Doggystyle"},
            {"artist": "A Tribe Called Quest", "title": "Can I Kick It?", "album": "People's Instinctive Travels"},
            {"artist": "Public Enemy", "title": "Fight the Power", "album": "Fear of a Black Planet"},
            {"artist": "De La Soul", "title": "Me Myself and I", "album": "3 Feet High and Rising"},
            {"artist": "Gang Starr", "title": "Mass Appeal", "album": "Hard to Earn"},
            {"artist": "Cypress Hill", "title": "Insane in the Brain", "album": "Black Sunday"},
            {"artist": "House of Pain", "title": "Jump Around", "album": "House of Pain"},
            {"artist": "Naughty by Nature", "title": "Hip Hop Hooray", "album": "19 Naughty III"},
            {"artist": "LL Cool J", "title": "Mama Said Knock You Out", "album": "Mama Said Knock You Out"},
            {"artist": "Ice Cube", "title": "It Was a Good Day", "album": "The Predator"}
        ]
    }
]

def generate_playlist_suggestions():
    """Generate 10 diverse playlist suggestions"""
    # Add more playlist templates
    additional_playlists = [
        {
            "id": "2000s_pop",
            "title": "2000s Pop Hits",
            "description": "Chart-topping pop anthems from the millennium",
            "category": "decade",
            "tags": ["2000s", "Pop", "Chart Hits", "Mainstream"],
            "tracks": [
                {"artist": "Britney Spears", "title": "Toxic", "album": "In the Zone"},
                {"artist": "Christina Aguilera", "title": "Genie in a Bottle", "album": "Christina Aguilera"},
                {"artist": "Kelly Clarkson", "title": "Since U Been Gone", "album": "Breakaway"},
                {"artist": "Pink", "title": "Get the Party Started", "album": "Missundaztood"},
                {"artist": "Avril Lavigne", "title": "Complicated", "album": "Let Go"},
                {"artist": "Gwen Stefani", "title": "Hollaback Girl", "album": "Love. Angel. Music. Baby."},
                {"artist": "Black Eyed Peas", "title": "I Gotta Feeling", "album": "The E.N.D."},
                {"artist": "Outkast", "title": "Hey Ya!", "album": "Speakerboxxx/The Love Below"},
                {"artist": "Usher", "title": "Yeah!", "album": "Confessions"},
                {"artist": "50 Cent", "title": "In da Club", "album": "Get Rich or Die Tryin'"},
                {"artist": "Eminem", "title": "Lose Yourself", "album": "8 Mile Soundtrack"},
                {"artist": "Nelly", "title": "Hot in Herre", "album": "Nellyville"},
                {"artist": "Missy Elliott", "title": "Work It", "album": "Under Construction"},
                {"artist": "Justin Timberlake", "title": "SexyBack", "album": "FutureSex/LoveSounds"},
                {"artist": "Beyoncé", "title": "Crazy in Love", "album": "Dangerously in Love"}
            ]
        },
        {
            "id": "high_energy_workout",
            "title": "High Energy Workout",
            "description": "Pump-up tracks to fuel your fitness routine",
            "category": "tempo",
            "tags": ["High Energy", "Workout", "Motivation", "Fast BPM"],
            "tracks": [
                {"artist": "Survivor", "title": "Eye of the Tiger", "album": "Eye of the Tiger"},
                {"artist": "Queen", "title": "We Will Rock You", "album": "News of the World"},
                {"artist": "Eminem", "title": "Till I Collapse", "album": "The Eminem Show"},
                {"artist": "Kanye West", "title": "Stronger", "album": "Graduation"},
                {"artist": "The White Stripes", "title": "Seven Nation Army", "album": "Elephant"},
                {"artist": "Rage Against the Machine", "title": "Killing in the Name", "album": "Rage Against the Machine"},
                {"artist": "Linkin Park", "title": "One Step Closer", "album": "Hybrid Theory"},
                {"artist": "Foo Fighters", "title": "The Pretender", "album": "Echoes, Silence, Patience & Grace"},
                {"artist": "Metallica", "title": "Enter Sandman", "album": "Metallica"},
                {"artist": "System of a Down", "title": "Chop Suey!", "album": "Toxicity"},
                {"artist": "Drowning Pool", "title": "Bodies", "album": "Sinner"},
                {"artist": "Limp Bizkit", "title": "Break Stuff", "album": "Significant Other"},
                {"artist": "Rob Zombie", "title": "Dragula", "album": "Hellbilly Deluxe"},
                {"artist": "Disturbed", "title": "Down with the Sickness", "album": "The Sickness"},
                {"artist": "Korn", "title": "Freak on a Leash", "album": "Follow the Leader"}
            ]
        },
        {
            "id": "chill_ambient",
            "title": "Chill Ambient",
            "description": "Relaxing atmospheric sounds for focus and calm",
            "category": "tempo",
            "tags": ["Chill", "Ambient", "Relaxing", "Low BPM"],
            "tracks": [
                {"artist": "Brian Eno", "title": "An Ending (Ascent)", "album": "Apollo: Atmospheres and Soundtracks"},
                {"artist": "Boards of Canada", "title": "Roygbiv", "album": "Music Has the Right to Children"},
                {"artist": "Aphex Twin", "title": "Xtal", "album": "Selected Ambient Works 85-92"},
                {"artist": "Tycho", "title": "A Walk", "album": "Dive"},
                {"artist": "Bonobo", "title": "Kiara", "album": "Black Sands"},
                {"artist": "Emancipator", "title": "Soon It Will Be Cold Enough", "album": "Soon It Will Be Cold Enough"},
                {"artist": "Thievery Corporation", "title": "Lebanese Blonde", "album": "The Richest Man in Babylon"},
                {"artist": "Zero 7", "title": "In the Waiting Line", "album": "Simple Things"},
                {"artist": "Massive Attack", "title": "Teardrop", "album": "Mezzanine"},
                {"artist": "Portishead", "title": "Glory Box", "album": "Dummy"},
                {"artist": "Air", "title": "La Femme d'Argent", "album": "Moon Safari"},
                {"artist": "Moby", "title": "Porcelain", "album": "Play"},
                {"artist": "Burial", "title": "Archangel", "album": "Untrue"},
                {"artist": "Ott", "title": "Queen of All Everything", "album": "Blumenkraft"},
                {"artist": "Carbon Based Lifeforms", "title": "Photosynthesis", "album": "Hydroponic Garden"}
            ]
        },
        {
            "id": "road_trip_anthems",
            "title": "Road Trip Anthems",
            "description": "Perfect driving songs for the open road",
            "category": "mood",
            "tags": ["Road Trip", "Driving", "Adventure", "Feel Good"],
            "tracks": [
                {"artist": "Tom Petty", "title": "Free Fallin'", "album": "Full Moon Fever"},
                {"artist": "Fleetwood Mac", "title": "Go Your Own Way", "album": "Rumours"},
                {"artist": "The Eagles", "title": "Take It Easy", "album": "Eagles"},
                {"artist": "Steppenwolf", "title": "Born to Be Wild", "album": "Steppenwolf"},
                {"artist": "Lynyrd Skynyrd", "title": "Free Bird", "album": "(Pronounced 'Lĕh-'nérd 'Skin-'nérd)"},
                {"artist": "Bruce Springsteen", "title": "Born to Run", "album": "Born to Run"},
                {"artist": "John Denver", "title": "Country Roads", "album": "Poems, Prayers & Promises"},
                {"artist": "The Allman Brothers Band", "title": "Ramblin' Man", "album": "Brothers and Sisters"},
                {"artist": "CCR", "title": "Born on the Bayou", "album": "Bayou Country"},
                {"artist": "The Doobie Brothers", "title": "Long Train Runnin'", "album": "The Captain and Me"},
                {"artist": "America", "title": "A Horse with No Name", "album": "America"},
                {"artist": "The Steve Miller Band", "title": "Fly Like an Eagle", "album": "Fly Like an Eagle"},
                {"artist": "Bad Company", "title": "Can't Get Enough", "album": "Bad Company"},
                {"artist": "Foghat", "title": "Slow Ride", "album": "Fool for the City"},
                {"artist": "Golden Earring", "title": "Radar Love", "album": "Moontan"}
            ]
        },
        {
            "id": "party_starters",
            "title": "Party Starters",
            "description": "Guaranteed crowd-pleasers to get any party going",
            "category": "mood",
            "tags": ["Party", "Dance", "Crowd Pleasers", "Fun"],
            "tracks": [
                {"artist": "Mark Ronson ft. Bruno Mars", "title": "Uptown Funk", "album": "Uptown Special"},
                {"artist": "Pharrell Williams", "title": "Happy", "album": "Girl"},
                {"artist": "Justin Timberlake", "title": "Can't Stop the Feeling!", "album": "Trolls Soundtrack"},
                {"artist": "Daft Punk ft. Pharrell Williams", "title": "Get Lucky", "album": "Random Access Memories"},
                {"artist": "Pitbull ft. Kesha", "title": "Timber", "album": "Global Warming: Meltdown"},
                {"artist": "LMFAO", "title": "Party Rock Anthem", "album": "Sorry for Party Rocking"},
                {"artist": "Flo Rida", "title": "Good Feeling", "album": "Wild Ones"},
                {"artist": "David Guetta ft. Sia", "title": "Titanium", "album": "Nothing but the Beat"},
                {"artist": "Calvin Harris", "title": "Feel So Close", "album": "18 Months"},
                {"artist": "Avicii", "title": "Wake Me Up", "album": "True"},
                {"artist": "Swedish House Mafia", "title": "Don't You Worry Child", "album": "Until Now"},
                {"artist": "Macklemore & Ryan Lewis", "title": "Can't Hold Us", "album": "The Heist"},
                {"artist": "Icona Pop", "title": "I Love It", "album": "Icona Pop"},
                {"artist": "Robin Thicke ft. T.I. & Pharrell", "title": "Blurred Lines", "album": "Blurred Lines"},
                {"artist": "Katy Perry", "title": "Roar", "album": "Prism"}
            ]
        }
    ]

    # Combine all playlists
    all_playlists = PLAYLIST_SUGGESTIONS + additional_playlists

    # Add estimated duration and shuffle tracks
    for playlist in all_playlists:
        # Estimate duration (3.5 minutes average per track)
        playlist['estimated_duration'] = len(playlist['tracks']) * 3.5
        # Shuffle tracks for variety
        random.shuffle(playlist['tracks'])
        # Add generation timestamp
        playlist['generated_at'] = datetime.now().isoformat()

    # Return 10 random playlists
    return random.sample(all_playlists, min(10, len(all_playlists)))

@app.route('/playlists/suggestions')
def get_playlist_suggestions():
    """Get AI-generated playlist suggestions"""
    try:
        suggestions = generate_playlist_suggestions()
        return jsonify({
            'success': True,
            'playlists': suggestions,
            'count': len(suggestions)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': f'Error generating suggestions: {str(e)}'})

@app.route('/playlists/check_library', methods=['POST'])
def check_playlist_against_library():
    """Check which tracks from a suggested playlist are NOT in the user's Plex library"""
    global plex_server

    if not plex_server:
        return jsonify({'success': False, 'error': 'Not connected to Plex server'})

    try:
        data = request.get_json()
        playlist_tracks = data.get('tracks', [])

        if not playlist_tracks:
            return jsonify({'success': False, 'error': 'No tracks provided'})

        # Get music library
        music_library = None
        for section in plex_server.library.sections():
            if section.type == 'artist':
                music_library = section
                break

        if not music_library:
            return jsonify({'success': False, 'error': 'No music library found'})

        # Get all tracks from Plex library for comparison
        existing_tracks = set()
        try:
            all_plex_tracks = music_library.searchTracks()
            for track in all_plex_tracks:
                # Create normalized identifiers for comparison
                track_key = f"{track.grandparentTitle or ''} - {track.title or ''}".lower().strip()
                existing_tracks.add(track_key)
        except Exception as e:
            print(f"Error fetching Plex tracks: {e}")

        # Check which suggested tracks are NOT in library
        missing_tracks = []
        existing_in_library = []

        for track in playlist_tracks:
            track_key = f"{track.get('artist', '')} - {track.get('title', '')}".lower().strip()

            if track_key in existing_tracks:
                existing_in_library.append(track)
            else:
                missing_tracks.append(track)

        return jsonify({
            'success': True,
            'missing_tracks': missing_tracks,
            'existing_tracks': existing_in_library,
            'missing_count': len(missing_tracks),
            'existing_count': len(existing_in_library),
            'total_tracks': len(playlist_tracks)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error checking library: {str(e)}'})

@app.route('/playlists/download_and_create', methods=['POST'])
def download_and_create_playlist():
    """Download missing tracks and create playlist in Plex"""
    global plex_server

    if not plex_server:
        return jsonify({'success': False, 'error': 'Not connected to Plex server'})

    try:
        data = request.get_json()
        playlist_name = data.get('playlist_name', '').strip()
        tracks_to_download = data.get('tracks', [])

        if not playlist_name:
            return jsonify({'success': False, 'error': 'Playlist name is required'})

        if not tracks_to_download:
            return jsonify({'success': False, 'error': 'No tracks to download'})

        # Start the download and playlist creation process
        # This will be handled asynchronously via socketio
        socketio.emit('playlist_download_started', {
            'playlist_name': playlist_name,
            'track_count': len(tracks_to_download),
            'message': f'Starting download of {len(tracks_to_download)} tracks for playlist "{playlist_name}"'
        })

        # Start background process
        threading.Thread(
            target=process_playlist_download_and_creation,
            args=(playlist_name, tracks_to_download),
            daemon=True
        ).start()

        return jsonify({
            'success': True,
            'message': f'Started downloading {len(tracks_to_download)} tracks for playlist "{playlist_name}"'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error starting playlist creation: {str(e)}'})

def process_playlist_download_and_creation(playlist_name, tracks):
    """Background process to download tracks and create playlist"""
    global plex_server

    try:
        downloaded_tracks = []
        failed_downloads = []

        socketio.emit('playlist_progress', {
            'playlist_name': playlist_name,
            'status': 'downloading',
            'progress': 0,
            'total': len(tracks),
            'message': 'Starting downloads...'
        })

        # Download each track
        for i, track in enumerate(tracks):
            try:
                query = f"{track.get('artist', '')} {track.get('title', '')}"
                socketio.emit('playlist_progress', {
                    'playlist_name': playlist_name,
                    'status': 'downloading',
                    'progress': i + 1,
                    'total': len(tracks),
                    'current_track': f"{track.get('artist', '')} - {track.get('title', '')}",
                    'message': f'Downloading: {query}'
                })

                # Use existing download functionality
                # Import the download function from the main script
                try:
                    # This would call the actual download function
                    # For now, we'll simulate success - in production you'd integrate with DeemixDownloader.py
                    import time
                    time.sleep(1)  # Simulate download time

                    # Simulate successful download
                    downloaded_tracks.append(track)

                except Exception as download_error:
                    failed_downloads.append({'track': track, 'error': str(download_error)})

            except Exception as e:
                failed_downloads.append({'track': track, 'error': str(e)})
                socketio.emit('playlist_progress', {
                    'playlist_name': playlist_name,
                    'status': 'error',
                    'message': f'Failed to download: {track.get("artist", "")} - {track.get("title", "")}'
                })

        # Create playlist in Plex if we have downloaded tracks
        if downloaded_tracks:
            socketio.emit('playlist_progress', {
                'playlist_name': playlist_name,
                'status': 'creating_playlist',
                'message': f'Creating playlist with {len(downloaded_tracks)} tracks...'
            })

            # Wait a moment for files to be processed
            time.sleep(3)

            # Refresh Plex library to pick up new files
            try:
                music_library = None
                for section in plex_server.library.sections():
                    if section.type == 'artist':
                        music_library = section
                        break

                if music_library:
                    music_library.update()
                    time.sleep(5)  # Wait for library update

                    # Find the newly downloaded tracks
                    playlist_tracks = []
                    for track in downloaded_tracks:
                        search_query = f"{track.get('artist', '')} {track.get('title', '')}"
                        search_results = music_library.searchTracks(title=track.get('title', ''))

                        for result in search_results:
                            if (result.grandparentTitle and
                                track.get('artist', '').lower() in result.grandparentTitle.lower()):
                                playlist_tracks.append(result)
                                break

                    # Create the playlist
                    if playlist_tracks:
                        playlist = plex_server.createPlaylist(playlist_name, items=playlist_tracks)

                        socketio.emit('playlist_completed', {
                            'playlist_name': playlist_name,
                            'success': True,
                            'downloaded_count': len(downloaded_tracks),
                            'failed_count': len(failed_downloads),
                            'playlist_id': playlist.ratingKey,
                            'message': f'✅ Playlist "{playlist_name}" created successfully with {len(playlist_tracks)} tracks!'
                        })
                    else:
                        socketio.emit('playlist_completed', {
                            'playlist_name': playlist_name,
                            'success': False,
                            'message': '❌ Could not find downloaded tracks in Plex library'
                        })

            except Exception as e:
                socketio.emit('playlist_completed', {
                    'playlist_name': playlist_name,
                    'success': False,
                    'message': f'❌ Error creating playlist: {str(e)}'
                })
        else:
            socketio.emit('playlist_completed', {
                'playlist_name': playlist_name,
                'success': False,
                'message': '❌ No tracks were successfully downloaded'
            })

    except Exception as e:
        socketio.emit('playlist_completed', {
            'playlist_name': playlist_name,
            'success': False,
            'message': f'❌ Error in playlist creation process: {str(e)}'
        })

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'message': 'Connected to Harmonix'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')

if __name__ == '__main__':
    # Ensure the script exists
    if not os.path.exists('DeemixDownloader.py'):
        print("Error: DeemixDownloader.py not found!")
        sys.exit(1)
    
    print("Starting Harmonix Web Interface...")
    print("Access the interface at: http://localhost:10000")
    
    socketio.run(app, host='0.0.0.0', port=10000, debug=False, allow_unsafe_werkzeug=True)
