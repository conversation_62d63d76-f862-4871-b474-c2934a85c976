from flask import Flask, render_template, jsonify, request
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import subprocess
import threading
import os
import sys
from datetime import datetime
import deezer
from plexapi.server import PlexServer
from plexapi.exceptions import BadRequest, NotFound, Unauthorized
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'deemix-downloader-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables to track script execution
script_process = None
script_running = False

# Global Plex connection
plex_server = None
plex_config = {
    'url': '',
    'token': '',
    'connected': False
}

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/status')
def status():
    return jsonify({
        'running': script_running,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/search', methods=['POST'])
def search_tracks():
    """Search for tracks using Deezer API"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()

        if not query:
            return jsonify({'success': False, 'error': 'Query is required'})

        # Initialize Deezer client
        dz = deezer.Deezer()

        # Search for tracks
        search_results = dz.api.search(query, limit=10)

        if not search_results or not search_results.get('data'):
            return jsonify({'success': True, 'results': []})

        # Format results for frontend
        results = []
        for track in search_results['data']:
            results.append({
                'id': track['id'],
                'title': track['title'],
                'artist': track['artist']['name'],
                'album': track['album']['title'] if track.get('album') else 'Unknown Album'
            })

        return jsonify({'success': True, 'results': results})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/download_track', methods=['POST'])
def download_single_track():
    """Download a single track using deemix CLI"""
    try:
        data = request.get_json()
        track_url = data.get('track_url', '').strip()
        title = data.get('title', 'Unknown')
        artist = data.get('artist', 'Unknown')

        if not track_url:
            return jsonify({'success': False, 'error': 'Track URL is required'})

        # Use deemix CLI to download the track
        result = subprocess.run([
            'python3', '-m', 'deemix',
            '--portable',
            track_url
        ], capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            return jsonify({
                'success': True,
                'message': f'✅ Successfully downloaded: {title} by {artist}'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Deemix CLI failed: {result.stderr or result.stdout}'
            })

    except subprocess.TimeoutExpired:
        return jsonify({'success': False, 'error': 'Download timeout (60s)'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def run_deemix_script():
    """Run the DeemixDownloader.py script and stream output"""
    global script_process, script_running
    
    try:
        script_running = True
        socketio.emit('script_started', {'message': 'Starting DeemixDownloader.py...'})
        
        # Change to the script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Run the script with real-time output
        script_process = subprocess.Popen(
            [sys.executable, 'DeemixDownloader.py'],
            cwd=script_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Stream output line by line
        for line in iter(script_process.stdout.readline, ''):
            if line:
                socketio.emit('script_output', {'message': line.rstrip()})
        
        # Wait for process to complete
        return_code = script_process.wait()
        
        if return_code == 0:
            socketio.emit('script_completed', {
                'message': 'DeemixDownloader.py completed successfully!',
                'success': True
            })
        else:
            socketio.emit('script_completed', {
                'message': f'DeemixDownloader.py failed with exit code {return_code}',
                'success': False
            })
            
    except Exception as e:
        socketio.emit('script_error', {
            'message': f'Error running script: {str(e)}',
            'success': False
        })
    finally:
        script_running = False
        script_process = None

@socketio.on('start_download')
def handle_start_download():
    """Handle request to start the download process"""
    global script_running
    
    if script_running:
        emit('error', {'message': 'Script is already running!'})
        return
    
    # Start the script in a separate thread
    thread = threading.Thread(target=run_deemix_script)
    thread.daemon = True
    thread.start()
    
    emit('download_started', {'message': 'Download process initiated'})

@socketio.on('stop_download')
def handle_stop_download():
    """Handle request to stop the download process"""
    global script_process, script_running
    
    if not script_running or not script_process:
        emit('error', {'message': 'No script is currently running!'})
        return
    
    try:
        script_process.terminate()
        script_process.wait(timeout=5)
        emit('download_stopped', {'message': 'Download process stopped'})
    except subprocess.TimeoutExpired:
        script_process.kill()
        emit('download_stopped', {'message': 'Download process forcefully terminated'})
    except Exception as e:
        emit('error', {'message': f'Error stopping script: {str(e)}'})
    finally:
        script_running = False
        script_process = None

# ---- Plex API Endpoints ----

@app.route('/plex/connect', methods=['POST'])
def connect_plex():
    """Connect to Plex server"""
    global plex_server, plex_config

    try:
        data = request.get_json()
        url = data.get('url', '').strip()
        token = data.get('token', '').strip()

        if not url or not token:
            return jsonify({'success': False, 'error': 'URL and token are required'})

        # Test connection
        plex_server = PlexServer(url, token)

        # Store config
        plex_config['url'] = url
        plex_config['token'] = token
        plex_config['connected'] = True

        return jsonify({
            'success': True,
            'message': f'Connected to Plex server: {plex_server.friendlyName}',
            'server_name': plex_server.friendlyName
        })

    except Unauthorized:
        return jsonify({'success': False, 'error': 'Invalid Plex token'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'Connection failed: {str(e)}'})

@app.route('/plex/status')
def plex_status():
    """Get Plex connection status"""
    return jsonify({
        'connected': plex_config['connected'],
        'server_name': plex_server.friendlyName if plex_server else None
    })

@app.route('/plex/music')
def get_plex_music():
    """Get music library from Plex"""
    global plex_server

    if not plex_server:
        return jsonify({'success': False, 'error': 'Not connected to Plex server'})

    try:
        # Get music library
        music_library = None
        for section in plex_server.library.sections():
            if section.type == 'artist':
                music_library = section
                break

        if not music_library:
            return jsonify({'success': False, 'error': 'No music library found on Plex server'})

        # Get all tracks
        tracks = []
        all_tracks = music_library.searchTracks()

        for track in all_tracks[:500]:  # Limit to 500 tracks for performance
            try:
                tracks.append({
                    'id': track.ratingKey,
                    'title': track.title,
                    'artist': track.grandparentTitle or 'Unknown Artist',
                    'album': track.parentTitle or 'Unknown Album',
                    'duration': track.duration // 1000 if track.duration else 0,  # Convert to seconds
                    'year': track.year or '',
                    'track_number': track.index or 0
                })
            except Exception as e:
                print(f"Error processing track: {e}")
                continue

        return jsonify({
            'success': True,
            'tracks': tracks,
            'total': len(tracks),
            'library_name': music_library.title
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Failed to fetch music library: {str(e)}'})

@app.route('/plex/playlist/create', methods=['POST'])
def create_plex_playlist():
    """Create a new playlist on Plex server"""
    global plex_server

    if not plex_server:
        return jsonify({'success': False, 'error': 'Not connected to Plex server'})

    try:
        data = request.get_json()
        playlist_name = data.get('name', '').strip()
        track_ids = data.get('track_ids', [])

        if not playlist_name:
            return jsonify({'success': False, 'error': 'Playlist name is required'})

        if not track_ids:
            return jsonify({'success': False, 'error': 'At least one track must be selected'})

        # Get track objects
        tracks = []
        for track_id in track_ids:
            try:
                track = plex_server.fetchItem(track_id)
                tracks.append(track)
            except Exception as e:
                print(f"Error fetching track {track_id}: {e}")
                continue

        if not tracks:
            return jsonify({'success': False, 'error': 'No valid tracks found'})

        # Create playlist
        playlist = plex_server.createPlaylist(playlist_name, tracks)

        return jsonify({
            'success': True,
            'message': f'Playlist "{playlist_name}" created successfully with {len(tracks)} tracks',
            'playlist_id': playlist.ratingKey,
            'track_count': len(tracks)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Failed to create playlist: {str(e)}'})

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'message': 'Connected to Harmonix'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')

if __name__ == '__main__':
    # Ensure the script exists
    if not os.path.exists('DeemixDownloader.py'):
        print("Error: DeemixDownloader.py not found!")
        sys.exit(1)
    
    print("Starting Harmonix Web Interface...")
    print("Access the interface at: http://localhost:10000")
    
    socketio.run(app, host='0.0.0.0', port=10000, debug=False, allow_unsafe_werkzeug=True)
