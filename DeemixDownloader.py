import os
import subprocess
import spotipy
from spotipy.oauth2 import SpotifyOAuth
import deezer
import json
import re
from collections import defaultdict
import time
from datetime import datetime

# ---- Config ----
SPOTIPY_CLIENT_ID = '57f12a98a5aa492cb8ce0e29d6065555'
SPOTIPY_CLIENT_SECRET = '97b45bb40d3f4cd393e4c55abe3cba5f'
SPOTIPY_REDIRECT_URI = 'http://127.0.0.1:8888/callback'

scope = "user-library-read playlist-read-private playlist-read-collaborative"

# Initialize Spotify client with cache directory
cache_path = os.path.join(os.path.dirname(__file__), 'cache', '.spotify_cache')
sp = None

# Initialize Deezer API (done once)
dz = None

# Global variables for duplicate detection
deemix_config = None
existing_files_cache = defaultdict(set)
download_stats = {
    'total': 0,
    'skipped': 0,
    'downloaded': 0,
    'failed': 0
}

# Global list to track failed downloads
failed_downloads = []

def load_deemix_config():
    """Load deemix configuration to understand file structure"""
    global deemix_config
    if deemix_config is None:
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                deemix_config = json.load(f)
            print("Deemix configuration loaded successfully")
            return True
        except Exception as e:
            print(f"Failed to load deemix config: {e}")
            return False
    return True

def init_spotify():
    """Initialize Spotify API with proper error handling for Docker environment"""
    global sp
    if sp is None:
        try:
            # Try to initialize Spotify with cache
            auth_manager = SpotifyOAuth(
                client_id=SPOTIPY_CLIENT_ID,
                client_secret=SPOTIPY_CLIENT_SECRET,
                redirect_uri=SPOTIPY_REDIRECT_URI,
                scope=scope,
                cache_path=cache_path,
                open_browser=False  # Don't try to open browser in Docker
            )

            sp = spotipy.Spotify(auth_manager=auth_manager)

            # Test the connection
            sp.current_user()
            print("Spotify API initialized successfully")
            return True

        except Exception as e:
            print(f"❌ Failed to initialize Spotify API: {e}")
            print("\n🔧 SPOTIFY AUTHENTICATION REQUIRED")
            print("=" * 50)
            print("To use this script, you need to authenticate with Spotify first.")
            print("Since this is running in Docker, please follow these steps:")
            print("\n1. Run this script locally (outside Docker) once to authenticate")
            print("2. Copy the generated .cache file to the cache/ directory")
            print("3. Or set up environment variables for authentication")
            print("\nFor now, the script will continue with Deezer-only functionality.")
            print("=" * 50)
            return False
    return True

def init_deezer():
    """Initialize Deezer API if not already done"""
    global dz
    if dz is None:
        try:
            # Create Deezer client for searching (using deezer-py)
            dz = deezer.Deezer()
            print("Deezer API initialized successfully")
            return True
        except Exception as e:
            print(f"Failed to initialize Deezer API: {e}")
            return False
    return True

def sanitize_filename(filename, illegal_char_replacer="_"):
    """Sanitize filename by replacing illegal characters"""
    # Common illegal characters for filenames
    illegal_chars = r'[<>:"/\\|?*]'
    return re.sub(illegal_chars, illegal_char_replacer, filename)

def predict_file_path(track_info, config):
    """Predict where deemix would save the file based on track info and config"""
    try:
        download_location = config['downloadLocation']

        # Extract track information
        title = track_info.get('title', 'Unknown Title')
        artist = track_info.get('artist', {}).get('name', 'Unknown Artist')
        album = track_info.get('album', {}).get('title', 'Unknown Album')

        # Sanitize names
        illegal_replacer = config.get('illegalCharacterReplacer', '_')
        title = sanitize_filename(title, illegal_replacer)
        artist = sanitize_filename(artist, illegal_replacer)
        album = sanitize_filename(album, illegal_replacer)

        # Build path based on config settings
        path_parts = [download_location]

        # Add artist folder if enabled
        if config.get('createArtistFolder', False):
            artist_template = config.get('artistNameTemplate', '%artist%')
            artist_folder = artist_template.replace('%artist%', artist)
            path_parts.append(artist_folder)

        # Add album folder if enabled
        if config.get('createAlbumFolder', False):
            album_template = config.get('albumNameTemplate', '%album%')
            album_folder = album_template.replace('%album%', album)
            path_parts.append(album_folder)

        # Add single folder if enabled and no album folder
        if config.get('createSingleFolder', False) and not config.get('createAlbumFolder', False):
            path_parts.append('Singles')

        # Generate filename
        track_template = config.get('tracknameTemplate', '%title%')
        filename = track_template.replace('%title%', title).replace('%artist%', artist)

        # Common audio file extensions
        possible_extensions = ['.flac', '.mp3', '.m4a', '.ogg']

        # Return list of possible file paths
        possible_paths = []
        for ext in possible_extensions:
            full_path = os.path.join(*path_parts, filename + ext)
            possible_paths.append(full_path)

        return possible_paths

    except Exception as e:
        print(f"Error predicting file path: {e}")
        return []

def build_existing_files_cache(download_location):
    """Build a cache of existing files for fast duplicate detection"""
    global existing_files_cache

    if not os.path.exists(download_location):
        print(f"Download location does not exist: {download_location}")
        return

    print("Building cache of existing files...")
    start_time = time.time()

    # Clear existing cache
    existing_files_cache.clear()

    # Walk through all directories and cache files
    for root, _, files in os.walk(download_location):
        for file in files:
            if file.lower().endswith(('.mp3', '.flac', '.m4a', '.ogg')):
                full_path = os.path.join(root, file)
                # Store both full path and normalized filename for fuzzy matching
                existing_files_cache['full_paths'].add(full_path.lower())

                # Create normalized filename for fuzzy matching
                normalized_name = re.sub(r'[^a-zA-Z0-9]', '', file.lower())
                existing_files_cache['normalized_names'].add(normalized_name)

                # Store directory-based lookup for faster searching
                dir_key = os.path.dirname(full_path).lower()
                existing_files_cache[dir_key].add(file.lower())

    elapsed = time.time() - start_time
    total_files = len(existing_files_cache['full_paths'])
    print(f"Cache built in {elapsed:.2f}s - Found {total_files} existing audio files")

def check_file_exists(track_info, config):
    """Check if a file already exists using multiple detection methods"""

    # Method 1: Exact path prediction
    predicted_paths = predict_file_path(track_info, config)
    for path in predicted_paths:
        if path.lower() in existing_files_cache['full_paths']:
            return True, f"Exact match: {path}"

    # Method 2: Check if files exist in filesystem (fallback)
    for path in predicted_paths:
        if os.path.exists(path):
            return True, f"File exists: {path}"

    # Method 3: Fuzzy matching based on normalized names
    title = track_info.get('title', '')
    artist = track_info.get('artist', {}).get('name', '')

    # Create normalized search string
    search_string = re.sub(r'[^a-zA-Z0-9]', '', f"{artist}{title}".lower())

    # Check for fuzzy matches
    for normalized_name in existing_files_cache['normalized_names']:
        if search_string in normalized_name or normalized_name in search_string:
            # Additional similarity check
            similarity = len(set(search_string) & set(normalized_name)) / max(len(search_string), len(normalized_name), 1)
            if similarity > 0.7:  # 70% similarity threshold
                return True, f"Fuzzy match found (similarity: {similarity:.2f})"

    return False, "No duplicate found"

def update_cache_with_new_file(track_info, config):
    """Update the cache with a newly downloaded file"""
    try:
        predicted_paths = predict_file_path(track_info, config)
        for path in predicted_paths:
            if os.path.exists(path):
                # Add to cache
                existing_files_cache['full_paths'].add(path.lower())

                # Add normalized name
                filename = os.path.basename(path)
                normalized_name = re.sub(r'[^a-zA-Z0-9]', '', filename.lower())
                existing_files_cache['normalized_names'].add(normalized_name)

                # Add to directory cache
                dir_key = os.path.dirname(path).lower()
                existing_files_cache[dir_key].add(filename.lower())
                break
    except Exception:
        # Silent fail - cache update is not critical
        pass

def record_failed_download(query, reason):
    """Record a failed download with query and reason"""
    global failed_downloads
    failed_downloads.append({
        'query': query,
        'reason': reason,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

def deemix_search_and_download(query):
    global download_stats
    download_stats['total'] += 1

    print(f"[{download_stats['total']}] Searching: {query}")

    # Initialize Deezer API and config if needed
    if not init_deezer():
        reason = "Deezer API initialization failed"
        print(f"Failed to download '{query}': {reason}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1
        return

    if not load_deemix_config():
        reason = "Config loading failed"
        print(f"Failed to download '{query}': {reason}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1
        return

    try:
        # Search for the track on Deezer (using deezer-py API)
        search_results = dz.api.search(query, limit=1)

        if not search_results or not search_results.get('data'):
            reason = "No results found on Deezer"
            print(f"No results found for '{query}'")
            record_failed_download(query, reason)
            download_stats['failed'] += 1
            return

        # Get the first track result
        track = search_results['data'][0]

        print(f"Found: {track['title']} by {track['artist']['name']}")

        # Convert track object to dict format for compatibility with existing code
        track_dict = {
            'id': track['id'],
            'title': track['title'],
            'artist': {'name': track['artist']['name']},
            'album': {'title': track['album']['title'] if track.get('album') else 'Unknown Album'}
        }

        # Check for duplicates before downloading
        exists, reason = check_file_exists(track_dict, deemix_config)
        if exists:
            print(f"⏭️  SKIPPED: {track['title']} by {track['artist']['name']} - {reason}")
            download_stats['skipped'] += 1
            return

        track_link = f"https://www.deezer.com/track/{track['id']}"
        print(f"⬇️  Downloading from: {track_link}")

        # Use deemix CLI tool to download
        try:
            # Use deemix command-line tool with the track URL
            result = subprocess.run([
                'python3', '-m', 'deemix',
                '--portable',
                track_link
            ], check=True, capture_output=True, text=True)

        except subprocess.CalledProcessError as e:
            raise Exception(f"Deemix CLI download failed: {str(e)}")
        except Exception as download_error:
            raise Exception(f"Deemix download failed: {str(download_error)}")

        print(f"✅ Successfully downloaded: {track['title']} by {track['artist']['name']}")
        download_stats['downloaded'] += 1

        # Update cache with newly downloaded file
        update_cache_with_new_file(track_dict, deemix_config)

    except Exception as e:
        if "Deemix download failed" in str(e):
            reason = str(e)
        else:
            reason = f"Download process failed: {str(e)}"
        print(f"❌ Failed to download '{query}': {e}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1
    except Exception as e:
        reason = f"Unexpected error: {str(e)}"
        print(f"❌ Failed to download '{query}': {e}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1

def print_download_statistics():
    """Print final download statistics"""
    print("\n" + "="*60)
    print("📊 DOWNLOAD STATISTICS")
    print("="*60)
    print(f"Total songs processed: {download_stats['total']}")
    print(f"✅ Successfully downloaded: {download_stats['downloaded']}")
    print(f"⏭️  Skipped (duplicates): {download_stats['skipped']}")
    print(f"❌ Failed: {download_stats['failed']}")

    if download_stats['total'] > 0:
        skip_percentage = (download_stats['skipped'] / download_stats['total']) * 100
        success_percentage = (download_stats['downloaded'] / download_stats['total']) * 100
        print(f"\nDuplicate detection saved {skip_percentage:.1f}% of downloads")
        print(f"Success rate: {success_percentage:.1f}%")
    print("="*60)

def save_failed_downloads():
    """Save failed downloads to a text file"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    failed_file_path = os.path.join(script_dir, 'failed_downloads.txt')

    try:
        with open(failed_file_path, 'w', encoding='utf-8') as f:
            # Write header with timestamp
            f.write(f"Failed Downloads Report\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")

            if not failed_downloads:
                f.write("🎉 No failed downloads! All songs were either downloaded successfully or skipped as duplicates.\n")
            else:
                f.write(f"Total failed downloads: {len(failed_downloads)}\n\n")

                for i, failure in enumerate(failed_downloads, 1):
                    f.write(f"{i}. Query: {failure['query']}\n")
                    f.write(f"   Reason: {failure['reason']}\n")
                    f.write(f"   Time: {failure['timestamp']}\n")
                    f.write("-" * 40 + "\n")

        if failed_downloads:
            print(f"\n📝 Failed downloads saved to: {failed_file_path}")
            print(f"   Total failures: {len(failed_downloads)}")
        else:
            print(f"\n📝 Failed downloads report saved to: {failed_file_path}")
            print("   No failures to report!")

    except Exception as e:
        print(f"\n⚠️  Warning: Could not save failed downloads file: {e}")

# ---- Spotify Helpers ----
def get_liked_tracks():
    liked = []
    results = sp.current_user_saved_tracks(limit=50)
    print("Fetching Liked Songs...")
    while results:
        for item in results['items']:
            track = item['track']
            track_str = f"{track['name']} {track['artists'][0]['name']}"
            print(f"  Liked: {track_str}")
            liked.append(track_str)
        if results['next']:
            results = sp.next(results)
        else:
            break
    return liked

def get_playlist_tracks():
    tracks = []
    playlists = sp.current_user_playlists()
    print("Fetching Playlists...")
    for playlist in playlists['items']:
        print(f"  Playlist: {playlist['name']}")
        results = sp.playlist_tracks(playlist['id'])
        for item in results['items']:
            track = item['track']
            if track:
                track_str = f"{track['name']} {track['artists'][0]['name']}"
                print(f"    Track: {track_str}")
                tracks.append(track_str)
    return tracks



# ---- Main ----
if __name__ == "__main__":
    print("🎵 Deemix Downloader with Duplicate Detection")
    print("=" * 50)

    # Load configuration first
    if not load_deemix_config():
        print("❌ Failed to load configuration. Exiting.")
        exit(1)

    # Build cache of existing files
    download_location = deemix_config.get('downloadLocation', '')
    if download_location:
        build_existing_files_cache(download_location)
    else:
        print("⚠️  Warning: No download location found in config. Duplicate detection may be limited.")

    print("\n🎧 Gathering songs from Spotify...")
    all_tracks = set()

    # Try to initialize Spotify
    if init_spotify():
        try:
            all_tracks.update(get_liked_tracks())
            all_tracks.update(get_playlist_tracks())
            # Note: get_discovery_weekly() removed due to Spotify API changes (Nov 2024)
            # Discover Weekly and other algorithmic playlists are no longer accessible via API
        except Exception as e:
            print(f"❌ Error gathering Spotify tracks: {e}")
            print("Continuing without Spotify tracks...")
    else:
        print("⚠️  Skipping Spotify - authentication not available")
        print("💡 You can still use this script by manually adding track queries")

        # For demo purposes, add some sample tracks if no Spotify
        sample_tracks = [
            "The Weeknd Blinding Lights",
            "Dua Lipa Levitating",
            "Ed Sheeran Shape of You",
            "Billie Eilish bad guy",
            "Post Malone Circles"
        ]

        print(f"\n🎵 Adding {len(sample_tracks)} sample tracks for demonstration:")
        for track in sample_tracks:
            print(f"  + {track}")
            all_tracks.add(track)

    if len(all_tracks) == 0:
        print("\n❌ No tracks found to download. Exiting.")
        exit(1)

    print(f"\n📋 Found {len(all_tracks)} unique songs. Starting download process...")
    print("🔍 Duplicate detection is enabled - existing files will be skipped\n")

    start_time = time.time()

    for track_query in all_tracks:
        deemix_search_and_download(track_query)

    # Print final statistics
    total_time = time.time() - start_time
    print(f"\n⏱️  Total processing time: {total_time:.2f} seconds")
    print_download_statistics()

    # Save failed downloads to file
    save_failed_downloads()
