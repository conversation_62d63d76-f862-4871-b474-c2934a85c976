version: '3.8'

services:
  deemix-downloader:
    build: .
    container_name: harmonix
    user: "0:0"
    ports:
      - "10000:10000"
    volumes:
      # Mount your music download directory
      - /mnt/Array/NAS/Music/Deemix:/app/downloads
      # Mount config directory to persist settings
      - /mnt/Rocket/Appdata/Harmonix/config:/app/config
      # Optional: Mount cache directory for better performance
      - /mnt/Rocket/Appdata/Harmonix/cache:/app/cache
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10000/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  downloads:
  cache:
