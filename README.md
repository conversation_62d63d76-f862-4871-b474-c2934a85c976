# 🎵 Harmonix

A simple Docker container that provides a web interface for running your DeemixDownloader.py script. Access a clean web UI on port 10000 to trigger downloads and monitor real-time progress.

## 📋 Prerequisites

1. **Docker Desktop** installed and running
   - Download from: https://www.docker.com/products/docker-desktop/
   - Make sure Docker Desktop is running before proceeding

2. **Spotify API Credentials** (already configured in your DeemixDownloader.py)
   - Your script already has the Spotify client ID and secret

3. **Deemix Configuration** (your existing config/config.json)
   - Your existing configuration will be used

## 🚀 Quick Start Guide

### Step 1: Download or Clone This Repository
If you haven't already, make sure all these files are in the same directory:
- `DeemixDownloader.py` (your existing script)
- `app.py` (Flask web server)
- `Dockerfile`
- `docker-compose.yml`
- `requirements.txt`
- `templates/index.html`
- `config/config.json` (your existing config)

### Step 2: Set Up Spotify Authentication (First Time Only)
1. Press `Windows + R`, type `cmd`, and press Enter
2. Navigate to your project directory:
   ```bash
   cd "C:\Users\<USER>\OneDrive\Desktop\Docker Deemix"
   ```
3. Run the authentication setup:
   ```bash
   python setup_spotify_auth.py
   ```
4. Follow the prompts to authenticate with Spotify
5. This creates a cache file that the Docker container will use

### Step 3: Build and Run the Container
Run this single command:
```bash
docker-compose up --build
```

**What this does:**
- Builds the Docker image with all dependencies
- Starts the container
- Maps port 10000 to your local machine
- Mounts your downloads and config directories

### Step 4: Access the Web Interface
1. Open your web browser
2. Go to: http://localhost:10000
3. You should see the Deemix Downloader web interface

### Step 5: Start Downloading
1. Click the "Start Download" button
2. Watch real-time console output as your script runs
3. Downloads will be saved to the `./downloads` directory

## 📁 Directory Structure

After running, your directory will look like this:
```
Docker Deemix/
├── DeemixDownloader.py      # Your original script
├── app.py                   # Flask web server
├── Dockerfile               # Docker configuration
├── docker-compose.yml       # Docker Compose configuration
├── requirements.txt         # Python dependencies
├── templates/
│   └── index.html          # Web interface
├── config/
│   └── config.json         # Deemix configuration
├── downloads/              # Downloaded music (created automatically)
└── cache/                  # Cache directory (created automatically)
```

## 🔧 Configuration

### Changing Download Location
Edit the `config/config.json` file and update the `downloadLocation` path:
```json
{
  "downloadLocation": "/app/downloads",
  ...
}
```

### Mounting Different Directories
Edit `docker-compose.yml` to change where files are saved:
```yaml
volumes:
  - "C:/Your/Music/Folder:/app/downloads"  # Change left side to your desired path
  - "./config:/app/config"
```

## 🛠️ Troubleshooting

### Container Won't Start
1. **Check Docker Desktop is running**
   ```bash
   docker --version
   ```

2. **Check if port 10000 is already in use**
   ```bash
   netstat -an | findstr :10000
   ```
   If something is using port 10000, change it in `docker-compose.yml`:
   ```yaml
   ports:
     - "10001:10000"  # Use port 10001 instead
   ```

### Web Interface Not Loading
1. **Verify container is running**
   ```bash
   docker-compose ps
   ```

2. **Check container logs**
   ```bash
   docker-compose logs -f
   ```

3. **Try different browser or incognito mode**

### Spotify Authentication Issues
1. **"Failed to initialize Spotify API" error**
   ```bash
   python setup_spotify_auth.py
   ```
   Run this outside Docker first to authenticate

2. **"EOF when reading a line" error**
   - This means Spotify authentication is needed
   - Run the setup script above
   - Make sure the cache/.spotify_cache file exists

3. **Authentication keeps failing**
   - Delete the cache/.spotify_cache file
   - Run setup_spotify_auth.py again
   - Make sure you complete the browser authentication

### Downloads Not Working
1. **Check Spotify authentication** (see above)

2. **Verify Deemix installation**
   ```bash
   docker-compose exec deemix-downloader deemix --version
   ```

3. **Check download permissions**
   ```bash
   docker-compose exec deemix-downloader ls -la /app/downloads
   ```

### Script Errors
1. **View real-time logs in web interface**
2. **Check container logs**
   ```bash
   docker-compose logs -f deemix-downloader
   ```

## 📱 Using the Web Interface

### Main Features
- **Start Download Button**: Begins the DeemixDownloader.py script
- **Stop Download Button**: Stops the running script
- **Real-time Console**: Shows live output from the script
- **Status Indicator**: Shows current state (Ready/Running/Error)

### Console Output Colors
- 🟢 **Green**: Successful downloads
- 🔴 **Red**: Errors and failures
- 🟡 **Yellow**: Warnings and skipped files
- 🔵 **Blue**: General information

## 🔄 Managing the Container

### Start the container
```bash
docker-compose up -d
```

### Stop the container
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f
```

### Rebuild after changes
```bash
docker-compose up --build
```

### Remove everything (including downloaded files)
```bash
docker-compose down -v
```

## 🆘 Getting Help

### Common Issues and Solutions

1. **"Permission denied" errors**
   - Make sure Docker Desktop has access to your drive
   - Go to Docker Desktop Settings > Resources > File Sharing

2. **"Port already in use" error**
   - Change the port in docker-compose.yml
   - Or stop the conflicting service

3. **Spotify authentication issues**
   - Check the container logs for the authentication URL
   - You may need to visit the URL manually

4. **Downloads not appearing**
   - Check the downloads directory is correctly mounted
   - Verify the downloadLocation in config.json

### Still Need Help?
1. Check the container logs: `docker-compose logs -f`
2. Verify all files are in the correct location
3. Make sure Docker Desktop is running and up to date
4. Try rebuilding: `docker-compose up --build`

## 🎯 Tips for Success

1. **First Run**: The first time you run this, Spotify might require authentication
2. **Patience**: Building the Docker image takes a few minutes the first time
3. **Monitoring**: Keep the web interface open to monitor progress
4. **Storage**: Make sure you have enough disk space for downloads
5. **Network**: Ensure stable internet connection for downloads

---

**Enjoy your automated music downloads! 🎵**
