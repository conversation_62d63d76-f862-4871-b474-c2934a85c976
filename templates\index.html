<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Harmonix</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #2E2E2E;
            min-height: 100vh;
            padding: 20px;
            color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #3A3A3A;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            border: 1px solid #4A4A4A;
        }

        .header {
            background: #2E2E2E;
            color: #FFFFFF;
            padding: 30px;
            text-align: center;
            border-bottom: 2px solid #00BCD4;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #00BCD4;
        }

        .header p {
            font-size: 1.1em;
            color: #CCCCCC;
        }

        .controls {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #4A4A4A;
            background: #3A3A3A;
        }

        .manual-search {
            padding: 30px;
            border-bottom: 1px solid #4A4A4A;
            background: #333333;
        }

        .manual-search h2 {
            margin-bottom: 20px;
            color: #00BCD4;
            text-align: center;
        }

        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
            justify-content: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #CCCCCC;
        }

        .form-group input {
            padding: 12px;
            border: 2px solid #555555;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .form-group input:focus {
            outline: none;
            border-color: #00BCD4;
            box-shadow: 0 0 5px rgba(0, 188, 212, 0.3);
        }

        .search-results {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 8px;
            background: #2E2E2E;
        }

        .search-results.hidden {
            display: none;
        }

        .result-item {
            padding: 15px;
            border-bottom: 1px solid #4A4A4A;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .result-item:hover {
            background-color: #3A3A3A;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-info {
            flex: 1;
        }

        .result-title {
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 5px;
        }

        .result-artist {
            color: #CCCCCC;
            font-size: 14px;
        }

        .result-album {
            color: #999999;
            font-size: 12px;
            margin-top: 2px;
        }

        .btn.small {
            padding: 8px 16px;
            font-size: 14px;
            min-width: auto;
        }

        .btn {
            background: linear-gradient(135deg, #00BCD4 0%, #33B5E5 100%);
            color: #FFFFFF;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            min-width: 150px;
            border: 1px solid #00BCD4;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 188, 212, 0.3);
            background: linear-gradient(135deg, #33B5E5 0%, #00BCD4 100%);
        }

        .btn:disabled {
            background: #555555;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            border: 1px solid #555555;
            color: #999999;
            text-shadow: none;
        }

        .btn.stop {
            background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
            border: 1px solid #F44336;
            display: none;
        }

        .btn.stop.visible {
            display: inline-block;
        }

        .btn.stop:hover {
            background: linear-gradient(135deg, #D32F2F 0%, #F44336 100%);
            box-shadow: 0 10px 20px rgba(244, 67, 54, 0.3);
        }

        .status {
            padding: 20px 30px;
            background: #333333;
            border-bottom: 1px solid #4A4A4A;
            color: #CCCCCC;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            background: #4CAF50;
        }

        .status-indicator.running {
            background: #00BCD4;
            animation: pulse 1.5s infinite;
        }

        .status-indicator.error {
            background: #F44336;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .console {
            background: #1A1A1A;
            color: #00FF00;
            font-family: 'Courier New', monospace;
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            font-size: 14px;
            line-height: 1.4;
            border: 1px solid #333333;
        }

        .console-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .console-line.success {
            color: #4CAF50;
        }

        .console-line.error {
            color: #F44336;
        }

        .console-line.warning {
            color: #FF9800;
        }

        .console-line.info {
            color: #00BCD4;
        }

        .console-line.timestamp {
            color: #777777;
            font-size: 12px;
        }

        .footer {
            padding: 20px 30px;
            text-align: center;
            color: #999999;
            font-size: 0.9em;
            background: #2E2E2E;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #555555;
            border-top: 3px solid #00BCD4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Additional dark theme enhancements */
        .form-group input::placeholder {
            color: #888888;
        }

        .search-results::-webkit-scrollbar {
            width: 8px;
        }

        .search-results::-webkit-scrollbar-track {
            background: #2E2E2E;
        }

        .search-results::-webkit-scrollbar-thumb {
            background: #555555;
            border-radius: 4px;
        }

        .search-results::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }

        .console::-webkit-scrollbar {
            width: 8px;
        }

        .console::-webkit-scrollbar-track {
            background: #1A1A1A;
        }

        .console::-webkit-scrollbar-thumb {
            background: #333333;
            border-radius: 4px;
        }

        .console::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }

        /* Tab Navigation Styles */
        .tab-navigation {
            background: #2E2E2E;
            border-bottom: 2px solid #4A4A4A;
            display: flex;
            padding: 0;
            margin: 0;
        }

        .tab-button {
            background: #3A3A3A;
            color: #CCCCCC;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            flex: 1;
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .tab-button:hover {
            background: #4A4A4A;
            color: #FFFFFF;
        }

        .tab-button.active {
            background: #00BCD4;
            color: #FFFFFF;
            border-bottom: 3px solid #33B5E5;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Playlist tab specific styles */
        .playlist-container {
            padding: 30px;
            text-align: center;
            color: #CCCCCC;
        }

        .playlist-header {
            color: #00BCD4;
            font-size: 2em;
            margin-bottom: 20px;
        }

        .playlist-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .playlist-section {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #4A4A4A;
        }

        .playlist-section h3 {
            color: #00BCD4;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .playlist-section p {
            color: #CCCCCC;
            line-height: 1.5;
        }

        .coming-soon {
            background: #333333;
            border: 2px dashed #555555;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
        }

        .coming-soon h2 {
            color: #00BCD4;
            margin-bottom: 15px;
        }

        .coming-soon p {
            color: #999999;
            font-size: 1.1em;
        }

        /* Plex Connection Styles */
        .plex-connection {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #4A4A4A;
        }

        .plex-connection.connected {
            border-color: #4CAF50;
            background: #2E4A2E;
        }

        .plex-connection.disconnected {
            border-color: #F44336;
            background: #4A2E2E;
        }

        .connection-form {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #F44336;
        }

        .status-dot.connected {
            background: #4CAF50;
        }

        /* Music Library Styles */
        .music-library {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .library-section {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #4A4A4A;
        }

        .library-section h3 {
            color: #00BCD4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-library {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .search-library input {
            flex: 1;
            padding: 10px;
            border: 2px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .track-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
        }

        .track-item {
            padding: 10px;
            border-bottom: 1px solid #4A4A4A;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: background-color 0.2s ease;
        }

        .track-item:hover {
            background-color: #3A3A3A;
        }

        .track-item:last-child {
            border-bottom: none;
        }

        .track-item.selected {
            background-color: #004A5A;
        }

        .track-checkbox {
            margin-right: 10px;
        }

        .track-info {
            flex: 1;
        }

        .track-title {
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 2px;
        }

        .track-details {
            font-size: 12px;
            color: #CCCCCC;
        }

        .track-duration {
            color: #999999;
            font-size: 12px;
        }

        /* Selected Songs Styles */
        .selected-songs {
            min-height: 200px;
        }

        .selected-track {
            padding: 8px;
            background: #004A5A;
            border-radius: 5px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: move;
        }

        .selected-track:hover {
            background: #005A6A;
        }

        .remove-track {
            background: #F44336;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 12px;
        }

        .remove-track:hover {
            background: #D32F2F;
        }

        /* Playlist Creation Styles */
        .playlist-creation {
            background: #333333;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #4A4A4A;
        }

        .playlist-form {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 15px;
        }

        .playlist-form input {
            flex: 1;
            padding: 12px;
            border: 2px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .selection-counter {
            color: #00BCD4;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .playlist-actions {
            display: flex;
            gap: 10px;
        }

        .btn.secondary {
            background: #555555;
            border: 1px solid #666666;
        }

        .btn.secondary:hover {
            background: #666666;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            z-index: 10;
        }

        .loading-overlay.hidden {
            display: none;
        }

        .drag-over {
            border: 2px dashed #00BCD4;
            background: rgba(0, 188, 212, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Harmonix</h1>
            <p>Download your Spotify library using Deemix with real-time progress tracking</p>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-button active" onclick="switchTab('download')">Download Music</button>
            <button class="tab-button" onclick="switchTab('playlists')">Playlists</button>
        </div>

        <!-- Download Music Tab Content -->
        <div id="download-tab" class="tab-content active">
            <div class="controls">
            <button id="startBtn" class="btn">
                <span id="startText">Start Download</span>
            </button>
            <button id="stopBtn" class="btn stop">
                Stop Download
            </button>
        </div>

        <div class="manual-search">
            <h2>🔍 Manual Track Search</h2>
            <div class="search-form">
                <div class="form-group">
                    <label for="artistInput">Artist:</label>
                    <input type="text" id="artistInput" placeholder="Enter artist name">
                </div>
                <div class="form-group">
                    <label for="albumInput">Album:</label>
                    <input type="text" id="albumInput" placeholder="Enter album name">
                </div>
                <div class="form-group">
                    <label for="trackInput">Track:</label>
                    <input type="text" id="trackInput" placeholder="Enter track name">
                </div>
                <div class="form-group">
                    <button id="searchBtn" class="btn">Search</button>
                </div>
            </div>
            <div id="searchResults" class="search-results hidden">
                <!-- Search results will be populated here -->
            </div>
        </div>

        <div class="status">
            <span id="statusIndicator" class="status-indicator"></span>
            <span id="statusText">Ready to start download</span>
        </div>

            <div class="console" id="console">
                <div class="console-line timestamp">Console output will appear here...</div>
            </div>
        </div>

        <!-- Playlists Tab Content -->
        <div id="playlists-tab" class="tab-content">
            <div class="playlist-container">
                <h2 class="playlist-header">🎵 Playlist Management</h2>

                <!-- Plex Connection Section -->
                <div id="plexConnection" class="plex-connection disconnected">
                    <div class="connection-status">
                        <div id="connectionDot" class="status-dot"></div>
                        <span id="connectionStatus">Not connected to Plex server</span>
                    </div>

                    <div class="connection-form">
                        <div class="form-group">
                            <label for="plexServerUrl">Plex Server URL:</label>
                            <input type="text" id="plexServerUrl" placeholder="http://192.168.1.100:32400" value="">
                        </div>
                        <div class="form-group">
                            <label for="plexToken">Plex Token:</label>
                            <input type="password" id="plexToken" placeholder="Your Plex authentication token" value="">
                        </div>
                        <div class="form-group">
                            <button id="connectPlexBtn" class="btn">Connect</button>
                        </div>
                    </div>
                </div>

                <!-- Music Library Section -->
                <div id="musicLibrarySection" class="music-library" style="display: none;">
                    <!-- Available Songs -->
                    <div class="library-section">
                        <h3>
                            🎵 Available Songs
                            <span id="availableCount" class="selection-counter">(0 songs)</span>
                        </h3>

                        <div class="search-library">
                            <input type="text" id="musicSearch" placeholder="Search your music library...">
                            <button id="searchMusicBtn" class="btn small">Search</button>
                            <button id="loadAllMusicBtn" class="btn small secondary">Load All</button>
                        </div>

                        <div id="availableTracks" class="track-list">
                            <div class="track-item">
                                <div class="track-info">
                                    <div class="track-title">Connect to Plex to see your music library</div>
                                    <div class="track-details">Your tracks will appear here</div>
                                </div>
                            </div>
                        </div>

                        <div id="availableTracksLoading" class="loading-overlay hidden">
                            <div class="loading"></div>
                        </div>
                    </div>

                    <!-- Selected Songs -->
                    <div class="library-section">
                        <h3>
                            📋 Selected Songs
                            <span id="selectedCount" class="selection-counter">(0 selected)</span>
                        </h3>

                        <div id="selectedTracks" class="track-list selected-songs">
                            <div class="track-item">
                                <div class="track-info">
                                    <div class="track-title">No songs selected</div>
                                    <div class="track-details">Select songs from the left to add them here</div>
                                </div>
                            </div>
                        </div>

                        <div class="playlist-actions" style="margin-top: 15px;">
                            <button id="clearSelectionBtn" class="btn secondary small">Clear All</button>
                            <button id="selectAllBtn" class="btn secondary small">Select All Visible</button>
                        </div>
                    </div>
                </div>

                <!-- Playlist Creation Section -->
                <div id="playlistCreationSection" class="playlist-creation" style="display: none;">
                    <h3 style="color: #00BCD4; margin-bottom: 15px;">🎵 Create New Playlist</h3>

                    <div class="playlist-form">
                        <div class="form-group" style="flex: 1;">
                            <label for="playlistName">Playlist Name:</label>
                            <input type="text" id="playlistName" placeholder="Enter playlist name...">
                        </div>
                        <div class="form-group">
                            <button id="createPlaylistBtn" class="btn" disabled>Create Playlist</button>
                        </div>
                    </div>

                    <div id="playlistStatus" style="margin-top: 10px; color: #CCCCCC;"></div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Harmonix Web Interface | Real-time console output</p>
        </div>
    </div>

    <script>
        const socket = io();
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const startText = document.getElementById('startText');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const console = document.getElementById('console');
        const searchBtn = document.getElementById('searchBtn');
        const artistInput = document.getElementById('artistInput');
        const albumInput = document.getElementById('albumInput');
        const trackInput = document.getElementById('trackInput');
        const searchResults = document.getElementById('searchResults');

        // Plex playlist elements
        const connectPlexBtn = document.getElementById('connectPlexBtn');
        const plexServerUrl = document.getElementById('plexServerUrl');
        const plexToken = document.getElementById('plexToken');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionDot = document.getElementById('connectionDot');
        const plexConnection = document.getElementById('plexConnection');
        const musicLibrarySection = document.getElementById('musicLibrarySection');
        const playlistCreationSection = document.getElementById('playlistCreationSection');
        const musicSearch = document.getElementById('musicSearch');
        const searchMusicBtn = document.getElementById('searchMusicBtn');
        const loadAllMusicBtn = document.getElementById('loadAllMusicBtn');
        const availableTracks = document.getElementById('availableTracks');
        const selectedTracks = document.getElementById('selectedTracks');
        const availableCount = document.getElementById('availableCount');
        const selectedCount = document.getElementById('selectedCount');
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        const selectAllBtn = document.getElementById('selectAllBtn');
        const playlistName = document.getElementById('playlistName');
        const createPlaylistBtn = document.getElementById('createPlaylistBtn');
        const playlistStatus = document.getElementById('playlistStatus');
        const availableTracksLoading = document.getElementById('availableTracksLoading');

        let isRunning = false;
        let selectedSongs = new Map(); // Map of track ID to track object
        let availableSongs = [];

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Show selected tab content
            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Add active class to clicked tab button
            const clickedButton = event.target;
            clickedButton.classList.add('active');
        }

        function addConsoleMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }

        function updateStatus(running, message) {
            isRunning = running;
            statusText.textContent = message;

            if (running) {
                statusIndicator.className = 'status-indicator running';
                startBtn.disabled = true;
                stopBtn.disabled = false;
                stopBtn.classList.add('visible');
                startText.innerHTML = '<span class="loading"></span>Running...';
            } else {
                statusIndicator.className = 'status-indicator';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                stopBtn.classList.remove('visible');
                startText.textContent = 'Start Download';
            }
        }

        // Socket event handlers
        socket.on('connected', (data) => {
            addConsoleMessage('Connected to server', 'success');
        });

        socket.on('script_started', (data) => {
            updateStatus(true, 'Download in progress...');
            addConsoleMessage(data.message, 'info');
        });

        socket.on('script_output', (data) => {
            let type = 'info';
            if (data.message.includes('✅')) type = 'success';
            else if (data.message.includes('❌')) type = 'error';
            else if (data.message.includes('⚠️')) type = 'warning';
            else if (data.message.includes('⏭️')) type = 'warning';
            
            addConsoleMessage(data.message, type);
        });

        socket.on('script_completed', (data) => {
            updateStatus(false, data.success ? 'Download completed successfully' : 'Download failed');
            addConsoleMessage(data.message, data.success ? 'success' : 'error');
        });

        socket.on('script_error', (data) => {
            updateStatus(false, 'Error occurred');
            statusIndicator.className = 'status-indicator error';
            addConsoleMessage(data.message, 'error');
        });

        socket.on('download_started', (data) => {
            addConsoleMessage(data.message, 'info');
        });

        socket.on('download_stopped', (data) => {
            updateStatus(false, 'Download stopped');
            addConsoleMessage(data.message, 'warning');
        });

        socket.on('error', (data) => {
            addConsoleMessage(data.message, 'error');
        });

        // Search functionality
        function performSearch() {
            const artist = artistInput.value.trim();
            const album = albumInput.value.trim();
            const track = trackInput.value.trim();

            if (!artist && !album && !track) {
                addConsoleMessage('Please enter at least an artist, album, or track name', 'warning');
                return;
            }

            const query = `${artist} ${album} ${track}`.trim();
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<span class="loading"></span>Searching...';

            addConsoleMessage(`Searching for: ${query}`, 'info');

            fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';

                if (data.success) {
                    displaySearchResults(data.results);
                    addConsoleMessage(`Found ${data.results.length} results`, 'success');
                } else {
                    addConsoleMessage(`Search failed: ${data.error}`, 'error');
                    searchResults.classList.add('hidden');
                }
            })
            .catch(error => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';
                addConsoleMessage(`Search error: ${error.message}`, 'error');
                searchResults.classList.add('hidden');
            });
        }

        function displaySearchResults(results) {
            searchResults.innerHTML = '';

            if (results.length === 0) {
                searchResults.innerHTML = '<div class="result-item">No results found</div>';
            } else {
                results.forEach((result, index) => {
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item';
                    resultItem.innerHTML = `
                        <div class="result-info">
                            <div class="result-title">${result.title}</div>
                            <div class="result-artist">by ${result.artist}</div>
                            <div class="result-album">Album: ${result.album}</div>
                        </div>
                        <button class="btn small" onclick="downloadTrack('${result.id}', '${result.title}', '${result.artist}')">
                            Download
                        </button>
                    `;
                    searchResults.appendChild(resultItem);
                });
            }

            searchResults.classList.remove('hidden');
        }

        function downloadTrack(trackId, title, artist) {
            const trackUrl = `https://www.deezer.com/track/${trackId}`;
            addConsoleMessage(`Starting download: ${title} by ${artist}`, 'info');

            fetch('/download_track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    track_url: trackUrl,
                    title: title,
                    artist: artist
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addConsoleMessage(data.message, 'success');
                } else {
                    addConsoleMessage(`Download failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                addConsoleMessage(`Download error: ${error.message}`, 'error');
            });
        }

        // Plex functionality
        function checkPlexConnection() {
            fetch('/plex/status')
                .then(response => response.json())
                .then(data => {
                    updatePlexConnectionStatus(data.connected, data.server_name);
                })
                .catch(error => {
                    console.error('Error checking Plex status:', error);
                    updatePlexConnectionStatus(false);
                });
        }

        function updatePlexConnectionStatus(connected, serverName = '') {
            if (connected) {
                connectionStatus.textContent = `Connected to ${serverName}`;
                connectionDot.classList.add('connected');
                plexConnection.classList.remove('disconnected');
                plexConnection.classList.add('connected');
                musicLibrarySection.style.display = 'grid';
                playlistCreationSection.style.display = 'block';
                connectPlexBtn.textContent = 'Reconnect';
            } else {
                connectionStatus.textContent = 'Not connected to Plex server';
                connectionDot.classList.remove('connected');
                plexConnection.classList.remove('connected');
                plexConnection.classList.add('disconnected');
                musicLibrarySection.style.display = 'none';
                playlistCreationSection.style.display = 'none';
                connectPlexBtn.textContent = 'Connect';
            }
        }

        function connectToPlex() {
            const serverUrl = plexServerUrl.value.trim();
            const token = plexToken.value.trim();

            if (!serverUrl || !token) {
                addConsoleMessage('Please enter both Plex server URL and token', 'warning');
                return;
            }

            connectPlexBtn.disabled = true;
            connectPlexBtn.innerHTML = '<span class="loading"></span>Connecting...';

            fetch('/plex/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    server_url: serverUrl,
                    token: token
                })
            })
            .then(response => response.json())
            .then(data => {
                connectPlexBtn.disabled = false;
                connectPlexBtn.textContent = 'Connect';

                if (data.success) {
                    addConsoleMessage(`✅ ${data.message}`, 'success');
                    updatePlexConnectionStatus(true, data.server_name);
                } else {
                    addConsoleMessage(`❌ Plex connection failed: ${data.error}`, 'error');
                    updatePlexConnectionStatus(false);
                }
            })
            .catch(error => {
                connectPlexBtn.disabled = false;
                connectPlexBtn.textContent = 'Connect';
                addConsoleMessage(`❌ Connection error: ${error.message}`, 'error');
                updatePlexConnectionStatus(false);
            });
        }

        function loadMusicLibrary() {
            availableTracksLoading.classList.remove('hidden');

            fetch('/plex/music')
                .then(response => response.json())
                .then(data => {
                    availableTracksLoading.classList.add('hidden');

                    if (data.success) {
                        availableSongs = data.tracks;
                        displayAvailableTracks(data.tracks);
                        availableCount.textContent = `(${data.tracks.length} songs)`;
                        addConsoleMessage(`✅ Loaded ${data.tracks.length} tracks from Plex library`, 'success');
                    } else {
                        addConsoleMessage(`❌ Failed to load music: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    availableTracksLoading.classList.add('hidden');
                    addConsoleMessage(`❌ Error loading music: ${error.message}`, 'error');
                });
        }

        function searchMusicLibrary() {
            const query = musicSearch.value.trim();
            if (!query) {
                addConsoleMessage('Please enter a search term', 'warning');
                return;
            }

            availableTracksLoading.classList.remove('hidden');

            fetch('/plex/search_music', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                availableTracksLoading.classList.add('hidden');

                if (data.success) {
                    availableSongs = data.tracks;
                    displayAvailableTracks(data.tracks);
                    availableCount.textContent = `(${data.tracks.length} results)`;
                    addConsoleMessage(`🔍 Found ${data.tracks.length} tracks matching "${query}"`, 'success');
                } else {
                    addConsoleMessage(`❌ Search failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                availableTracksLoading.classList.add('hidden');
                addConsoleMessage(`❌ Search error: ${error.message}`, 'error');
            });
        }

        function displayAvailableTracks(tracks) {
            availableTracks.innerHTML = '';

            if (tracks.length === 0) {
                availableTracks.innerHTML = `
                    <div class="track-item">
                        <div class="track-info">
                            <div class="track-title">No tracks found</div>
                            <div class="track-details">Try a different search or load all music</div>
                        </div>
                    </div>
                `;
                return;
            }

            tracks.forEach(track => {
                const isSelected = selectedSongs.has(track.id);
                const trackElement = document.createElement('div');
                trackElement.className = `track-item ${isSelected ? 'selected' : ''}`;
                trackElement.innerHTML = `
                    <input type="checkbox" class="track-checkbox" ${isSelected ? 'checked' : ''}
                           onchange="toggleTrackSelection('${track.id}')">
                    <div class="track-info">
                        <div class="track-title">${track.title}</div>
                        <div class="track-details">${track.artist} • ${track.album}</div>
                    </div>
                    <div class="track-duration">${formatDuration(track.duration)}</div>
                `;
                availableTracks.appendChild(trackElement);
            });
        }

        function formatDuration(milliseconds) {
            if (!milliseconds) return '--:--';
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        function toggleTrackSelection(trackId) {
            const track = availableSongs.find(t => t.id === trackId);
            if (!track) return;

            if (selectedSongs.has(trackId)) {
                selectedSongs.delete(trackId);
            } else {
                selectedSongs.set(trackId, track);
            }

            updateSelectedTracksDisplay();
            updateCreatePlaylistButton();

            // Update the checkbox in available tracks
            displayAvailableTracks(availableSongs);
        }

        function updateSelectedTracksDisplay() {
            selectedCount.textContent = `(${selectedSongs.size} selected)`;

            if (selectedSongs.size === 0) {
                selectedTracks.innerHTML = `
                    <div class="track-item">
                        <div class="track-info">
                            <div class="track-title">No songs selected</div>
                            <div class="track-details">Select songs from the left to add them here</div>
                        </div>
                    </div>
                `;
                return;
            }

            selectedTracks.innerHTML = '';
            Array.from(selectedSongs.values()).forEach((track, index) => {
                const trackElement = document.createElement('div');
                trackElement.className = 'selected-track';
                trackElement.draggable = true;
                trackElement.dataset.trackId = track.id;
                trackElement.innerHTML = `
                    <div class="track-info">
                        <div class="track-title">${track.title}</div>
                        <div class="track-details">${track.artist} • ${track.album}</div>
                    </div>
                    <button class="remove-track" onclick="removeTrackFromSelection('${track.id}')">×</button>
                `;

                // Add drag and drop event listeners
                trackElement.addEventListener('dragstart', handleDragStart);
                trackElement.addEventListener('dragover', handleDragOver);
                trackElement.addEventListener('drop', handleDrop);
                trackElement.addEventListener('dragend', handleDragEnd);

                selectedTracks.appendChild(trackElement);
            });
        }

        function removeTrackFromSelection(trackId) {
            selectedSongs.delete(trackId);
            updateSelectedTracksDisplay();
            updateCreatePlaylistButton();
            displayAvailableTracks(availableSongs);
        }

        function clearAllSelection() {
            selectedSongs.clear();
            updateSelectedTracksDisplay();
            updateCreatePlaylistButton();
            displayAvailableTracks(availableSongs);
            addConsoleMessage('All selections cleared', 'info');
        }

        function selectAllVisible() {
            availableSongs.forEach(track => {
                selectedSongs.set(track.id, track);
            });
            updateSelectedTracksDisplay();
            updateCreatePlaylistButton();
            displayAvailableTracks(availableSongs);
            addConsoleMessage(`Selected ${availableSongs.length} visible tracks`, 'info');
        }

        function updateCreatePlaylistButton() {
            const hasName = playlistName.value.trim().length > 0;
            const hasSelection = selectedSongs.size > 0;
            createPlaylistBtn.disabled = !(hasName && hasSelection);

            if (hasSelection) {
                playlistStatus.textContent = `Ready to create playlist with ${selectedSongs.size} songs`;
                playlistStatus.style.color = '#4CAF50';
            } else {
                playlistStatus.textContent = 'Select songs to create a playlist';
                playlistStatus.style.color = '#CCCCCC';
            }
        }

        function createPlaylist() {
            const name = playlistName.value.trim();
            const trackIds = Array.from(selectedSongs.keys());

            if (!name || trackIds.length === 0) {
                addConsoleMessage('Please enter a playlist name and select songs', 'warning');
                return;
            }

            // Confirmation dialog
            if (!confirm(`Create playlist "${name}" with ${trackIds.length} songs?`)) {
                return;
            }

            createPlaylistBtn.disabled = true;
            createPlaylistBtn.innerHTML = '<span class="loading"></span>Creating...';
            playlistStatus.textContent = 'Creating playlist...';
            playlistStatus.style.color = '#00BCD4';

            fetch('/plex/create_playlist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    track_ids: trackIds
                })
            })
            .then(response => response.json())
            .then(data => {
                createPlaylistBtn.disabled = false;
                createPlaylistBtn.textContent = 'Create Playlist';

                if (data.success) {
                    addConsoleMessage(`✅ ${data.message}`, 'success');
                    playlistStatus.textContent = `✅ Playlist created successfully!`;
                    playlistStatus.style.color = '#4CAF50';

                    // Clear form
                    playlistName.value = '';
                    clearAllSelection();
                } else {
                    addConsoleMessage(`❌ Failed to create playlist: ${data.error}`, 'error');
                    playlistStatus.textContent = `❌ Failed to create playlist`;
                    playlistStatus.style.color = '#F44336';
                }
            })
            .catch(error => {
                createPlaylistBtn.disabled = false;
                createPlaylistBtn.textContent = 'Create Playlist';
                addConsoleMessage(`❌ Error creating playlist: ${error.message}`, 'error');
                playlistStatus.textContent = `❌ Error creating playlist`;
                playlistStatus.style.color = '#F44336';
            });
        }

        // Drag and drop functionality
        let draggedElement = null;

        function handleDragStart(e) {
            draggedElement = this;
            this.style.opacity = '0.5';
        }

        function handleDragOver(e) {
            e.preventDefault();
            return false;
        }

        function handleDrop(e) {
            e.preventDefault();

            if (this !== draggedElement) {
                const allTracks = Array.from(selectedTracks.children);
                const draggedIndex = allTracks.indexOf(draggedElement);
                const targetIndex = allTracks.indexOf(this);

                if (draggedIndex < targetIndex) {
                    this.parentNode.insertBefore(draggedElement, this.nextSibling);
                } else {
                    this.parentNode.insertBefore(draggedElement, this);
                }

                // Update the selectedSongs map order
                const orderedTracks = Array.from(selectedTracks.children).map(el => {
                    const trackId = el.dataset.trackId;
                    return selectedSongs.get(trackId);
                });

                selectedSongs.clear();
                orderedTracks.forEach(track => selectedSongs.set(track.id, track));
            }

            return false;
        }

        function handleDragEnd(e) {
            this.style.opacity = '';
            draggedElement = null;
        }

        // Button event handlers
        startBtn.addEventListener('click', () => {
            if (!isRunning) {
                socket.emit('start_download');
                addConsoleMessage('Requesting download start...', 'info');
            }
        });

        stopBtn.addEventListener('click', () => {
            if (isRunning) {
                socket.emit('stop_download');
                addConsoleMessage('Requesting download stop...', 'warning');
            }
        });

        searchBtn.addEventListener('click', performSearch);

        // Enter key support for search inputs
        artistInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        albumInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        trackInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        // Plex event listeners
        connectPlexBtn.addEventListener('click', connectToPlex);
        searchMusicBtn.addEventListener('click', searchMusicLibrary);
        loadAllMusicBtn.addEventListener('click', loadMusicLibrary);
        clearSelectionBtn.addEventListener('click', clearAllSelection);
        selectAllBtn.addEventListener('click', selectAllVisible);
        createPlaylistBtn.addEventListener('click', createPlaylist);

        playlistName.addEventListener('input', updateCreatePlaylistButton);

        musicSearch.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') searchMusicLibrary();
        });

        // Initial status check
        fetch('/status')
            .then(response => response.json())
            .then(data => {
                updateStatus(data.running, data.running ? 'Download in progress...' : 'Ready to start download');
            })
            .catch(error => {
                addConsoleMessage('Failed to get initial status', 'error');
            });

        // Check Plex connection on page load
        checkPlexConnection();
    </script>
</body>
</html>
