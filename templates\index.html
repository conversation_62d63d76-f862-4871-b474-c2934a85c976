<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Harmonix</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #2E2E2E;
            min-height: 100vh;
            padding: 20px;
            color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #3A3A3A;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            border: 1px solid #4A4A4A;
        }

        .header {
            background: #2E2E2E;
            color: #FFFFFF;
            padding: 30px;
            text-align: center;
            border-bottom: 2px solid #00BCD4;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #00BCD4;
        }

        .header p {
            font-size: 1.1em;
            color: #CCCCCC;
        }

        .controls {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #4A4A4A;
            background: #3A3A3A;
        }

        .manual-search {
            padding: 30px;
            border-bottom: 1px solid #4A4A4A;
            background: #333333;
        }

        .manual-search h2 {
            margin-bottom: 20px;
            color: #00BCD4;
            text-align: center;
        }

        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
            justify-content: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #CCCCCC;
        }

        .form-group input {
            padding: 12px;
            border: 2px solid #555555;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .form-group input:focus {
            outline: none;
            border-color: #00BCD4;
            box-shadow: 0 0 5px rgba(0, 188, 212, 0.3);
        }

        .search-results {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 8px;
            background: #2E2E2E;
        }

        .search-results.hidden {
            display: none;
        }

        .result-item {
            padding: 15px;
            border-bottom: 1px solid #4A4A4A;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .result-item:hover {
            background-color: #3A3A3A;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-info {
            flex: 1;
        }

        .result-title {
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 5px;
        }

        .result-artist {
            color: #CCCCCC;
            font-size: 14px;
        }

        .result-album {
            color: #999999;
            font-size: 12px;
            margin-top: 2px;
        }

        .btn.small {
            padding: 8px 16px;
            font-size: 14px;
            min-width: auto;
        }

        .btn {
            background: linear-gradient(135deg, #00BCD4 0%, #33B5E5 100%);
            color: #FFFFFF;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            min-width: 150px;
            border: 1px solid #00BCD4;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 188, 212, 0.3);
            background: linear-gradient(135deg, #33B5E5 0%, #00BCD4 100%);
        }

        .btn:disabled {
            background: #555555;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            border: 1px solid #555555;
            color: #999999;
            text-shadow: none;
        }

        .btn.stop {
            background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
            border: 1px solid #F44336;
            display: none;
        }

        .btn.stop.visible {
            display: inline-block;
        }

        .btn.stop:hover {
            background: linear-gradient(135deg, #D32F2F 0%, #F44336 100%);
            box-shadow: 0 10px 20px rgba(244, 67, 54, 0.3);
        }

        .status {
            padding: 20px 30px;
            background: #333333;
            border-bottom: 1px solid #4A4A4A;
            color: #CCCCCC;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            background: #4CAF50;
        }

        .status-indicator.running {
            background: #00BCD4;
            animation: pulse 1.5s infinite;
        }

        .status-indicator.error {
            background: #F44336;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .console {
            background: #1A1A1A;
            color: #00FF00;
            font-family: 'Courier New', monospace;
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            font-size: 14px;
            line-height: 1.4;
            border: 1px solid #333333;
        }

        .console-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .console-line.success {
            color: #4CAF50;
        }

        .console-line.error {
            color: #F44336;
        }

        .console-line.warning {
            color: #FF9800;
        }

        .console-line.info {
            color: #00BCD4;
        }

        .console-line.timestamp {
            color: #777777;
            font-size: 12px;
        }

        .footer {
            padding: 20px 30px;
            text-align: center;
            color: #999999;
            font-size: 0.9em;
            background: #2E2E2E;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #555555;
            border-top: 3px solid #00BCD4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Additional dark theme enhancements */
        .form-group input::placeholder {
            color: #888888;
        }

        .search-results::-webkit-scrollbar {
            width: 8px;
        }

        .search-results::-webkit-scrollbar-track {
            background: #2E2E2E;
        }

        .search-results::-webkit-scrollbar-thumb {
            background: #555555;
            border-radius: 4px;
        }

        .search-results::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }

        .console::-webkit-scrollbar {
            width: 8px;
        }

        .console::-webkit-scrollbar-track {
            background: #1A1A1A;
        }

        .console::-webkit-scrollbar-thumb {
            background: #333333;
            border-radius: 4px;
        }

        .console::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Harmonix</h1>
            <p>Download your Spotify library using Deemix with real-time progress tracking</p>
        </div>

        <div class="controls">
            <button id="startBtn" class="btn">
                <span id="startText">Start Download</span>
            </button>
            <button id="stopBtn" class="btn stop">
                Stop Download
            </button>
        </div>

        <div class="manual-search">
            <h2>🔍 Manual Track Search</h2>
            <div class="search-form">
                <div class="form-group">
                    <label for="artistInput">Artist:</label>
                    <input type="text" id="artistInput" placeholder="Enter artist name">
                </div>
                <div class="form-group">
                    <label for="trackInput">Track:</label>
                    <input type="text" id="trackInput" placeholder="Enter track name">
                </div>
                <div class="form-group">
                    <button id="searchBtn" class="btn">Search</button>
                </div>
            </div>
            <div id="searchResults" class="search-results hidden">
                <!-- Search results will be populated here -->
            </div>
        </div>

        <div class="status">
            <span id="statusIndicator" class="status-indicator"></span>
            <span id="statusText">Ready to start download</span>
        </div>

        <div class="console" id="console">
            <div class="console-line timestamp">Console output will appear here...</div>
        </div>

        <div class="footer">
            <p>Harmonix Web Interface | Real-time console output</p>
        </div>
    </div>

    <script>
        const socket = io();
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const startText = document.getElementById('startText');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const console = document.getElementById('console');
        const searchBtn = document.getElementById('searchBtn');
        const artistInput = document.getElementById('artistInput');
        const trackInput = document.getElementById('trackInput');
        const searchResults = document.getElementById('searchResults');

        let isRunning = false;

        function addConsoleMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }

        function updateStatus(running, message) {
            isRunning = running;
            statusText.textContent = message;

            if (running) {
                statusIndicator.className = 'status-indicator running';
                startBtn.disabled = true;
                stopBtn.disabled = false;
                stopBtn.classList.add('visible');
                startText.innerHTML = '<span class="loading"></span>Running...';
            } else {
                statusIndicator.className = 'status-indicator';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                stopBtn.classList.remove('visible');
                startText.textContent = 'Start Download';
            }
        }

        // Socket event handlers
        socket.on('connected', (data) => {
            addConsoleMessage('Connected to server', 'success');
        });

        socket.on('script_started', (data) => {
            updateStatus(true, 'Download in progress...');
            addConsoleMessage(data.message, 'info');
        });

        socket.on('script_output', (data) => {
            let type = 'info';
            if (data.message.includes('✅')) type = 'success';
            else if (data.message.includes('❌')) type = 'error';
            else if (data.message.includes('⚠️')) type = 'warning';
            else if (data.message.includes('⏭️')) type = 'warning';
            
            addConsoleMessage(data.message, type);
        });

        socket.on('script_completed', (data) => {
            updateStatus(false, data.success ? 'Download completed successfully' : 'Download failed');
            addConsoleMessage(data.message, data.success ? 'success' : 'error');
        });

        socket.on('script_error', (data) => {
            updateStatus(false, 'Error occurred');
            statusIndicator.className = 'status-indicator error';
            addConsoleMessage(data.message, 'error');
        });

        socket.on('download_started', (data) => {
            addConsoleMessage(data.message, 'info');
        });

        socket.on('download_stopped', (data) => {
            updateStatus(false, 'Download stopped');
            addConsoleMessage(data.message, 'warning');
        });

        socket.on('error', (data) => {
            addConsoleMessage(data.message, 'error');
        });

        // Search functionality
        function performSearch() {
            const artist = artistInput.value.trim();
            const track = trackInput.value.trim();

            if (!artist && !track) {
                addConsoleMessage('Please enter at least an artist or track name', 'warning');
                return;
            }

            const query = `${artist} ${track}`.trim();
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<span class="loading"></span>Searching...';

            addConsoleMessage(`Searching for: ${query}`, 'info');

            fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';

                if (data.success) {
                    displaySearchResults(data.results);
                    addConsoleMessage(`Found ${data.results.length} results`, 'success');
                } else {
                    addConsoleMessage(`Search failed: ${data.error}`, 'error');
                    searchResults.classList.add('hidden');
                }
            })
            .catch(error => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';
                addConsoleMessage(`Search error: ${error.message}`, 'error');
                searchResults.classList.add('hidden');
            });
        }

        function displaySearchResults(results) {
            searchResults.innerHTML = '';

            if (results.length === 0) {
                searchResults.innerHTML = '<div class="result-item">No results found</div>';
            } else {
                results.forEach((result, index) => {
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item';
                    resultItem.innerHTML = `
                        <div class="result-info">
                            <div class="result-title">${result.title}</div>
                            <div class="result-artist">by ${result.artist}</div>
                            <div class="result-album">Album: ${result.album}</div>
                        </div>
                        <button class="btn small" onclick="downloadTrack('${result.id}', '${result.title}', '${result.artist}')">
                            Download
                        </button>
                    `;
                    searchResults.appendChild(resultItem);
                });
            }

            searchResults.classList.remove('hidden');
        }

        function downloadTrack(trackId, title, artist) {
            const trackUrl = `https://www.deezer.com/track/${trackId}`;
            addConsoleMessage(`Starting download: ${title} by ${artist}`, 'info');

            fetch('/download_track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    track_url: trackUrl,
                    title: title,
                    artist: artist
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addConsoleMessage(data.message, 'success');
                } else {
                    addConsoleMessage(`Download failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                addConsoleMessage(`Download error: ${error.message}`, 'error');
            });
        }

        // Button event handlers
        startBtn.addEventListener('click', () => {
            if (!isRunning) {
                socket.emit('start_download');
                addConsoleMessage('Requesting download start...', 'info');
            }
        });

        stopBtn.addEventListener('click', () => {
            if (isRunning) {
                socket.emit('stop_download');
                addConsoleMessage('Requesting download stop...', 'warning');
            }
        });

        searchBtn.addEventListener('click', performSearch);

        // Enter key support for search inputs
        artistInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        trackInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        // Initial status check
        fetch('/status')
            .then(response => response.json())
            .then(data => {
                updateStatus(data.running, data.running ? 'Download in progress...' : 'Ready to start download');
            })
            .catch(error => {
                addConsoleMessage('Failed to get initial status', 'error');
            });
    </script>
</body>
</html>
