<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Harmonix</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #2E2E2E;
            min-height: 100vh;
            padding: 20px;
            color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #3A3A3A;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            border: 1px solid #4A4A4A;
        }

        .header {
            background: #2E2E2E;
            color: #FFFFFF;
            padding: 30px;
            text-align: center;
            border-bottom: 2px solid #00BCD4;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #00BCD4;
        }

        .header p {
            font-size: 1.1em;
            color: #CCCCCC;
        }

        .controls {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #4A4A4A;
            background: #3A3A3A;
        }

        .manual-search {
            padding: 30px;
            border-bottom: 1px solid #4A4A4A;
            background: #333333;
        }

        .manual-search h2 {
            margin-bottom: 20px;
            color: #00BCD4;
            text-align: center;
        }

        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
            justify-content: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #CCCCCC;
        }

        .form-group input {
            padding: 12px;
            border: 2px solid #555555;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .form-group input:focus {
            outline: none;
            border-color: #00BCD4;
            box-shadow: 0 0 5px rgba(0, 188, 212, 0.3);
        }

        .search-results {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 8px;
            background: #2E2E2E;
        }

        .search-results.hidden {
            display: none;
        }

        .result-item {
            padding: 15px;
            border-bottom: 1px solid #4A4A4A;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .result-item:hover {
            background-color: #3A3A3A;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-info {
            flex: 1;
        }

        .result-title {
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 5px;
        }

        .result-artist {
            color: #CCCCCC;
            font-size: 14px;
        }

        .result-album {
            color: #999999;
            font-size: 12px;
            margin-top: 2px;
        }

        .btn.small {
            padding: 8px 16px;
            font-size: 14px;
            min-width: auto;
        }

        .btn {
            background: linear-gradient(135deg, #00BCD4 0%, #33B5E5 100%);
            color: #FFFFFF;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            min-width: 150px;
            border: 1px solid #00BCD4;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 188, 212, 0.3);
            background: linear-gradient(135deg, #33B5E5 0%, #00BCD4 100%);
        }

        .btn:disabled {
            background: #555555;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            border: 1px solid #555555;
            color: #999999;
            text-shadow: none;
        }

        .btn.stop {
            background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
            border: 1px solid #F44336;
            display: none;
        }

        .btn.stop.visible {
            display: inline-block;
        }

        .btn.stop:hover {
            background: linear-gradient(135deg, #D32F2F 0%, #F44336 100%);
            box-shadow: 0 10px 20px rgba(244, 67, 54, 0.3);
        }

        .status {
            padding: 20px 30px;
            background: #333333;
            border-bottom: 1px solid #4A4A4A;
            color: #CCCCCC;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            background: #4CAF50;
        }

        .status-indicator.running {
            background: #00BCD4;
            animation: pulse 1.5s infinite;
        }

        .status-indicator.error {
            background: #F44336;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .console {
            background: #1A1A1A;
            color: #00FF00;
            font-family: 'Courier New', monospace;
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            font-size: 14px;
            line-height: 1.4;
            border: 1px solid #333333;
        }

        .console-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .console-line.success {
            color: #4CAF50;
        }

        .console-line.error {
            color: #F44336;
        }

        .console-line.warning {
            color: #FF9800;
        }

        .console-line.info {
            color: #00BCD4;
        }

        .console-line.timestamp {
            color: #777777;
            font-size: 12px;
        }

        .footer {
            padding: 20px 30px;
            text-align: center;
            color: #999999;
            font-size: 0.9em;
            background: #2E2E2E;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #555555;
            border-top: 3px solid #00BCD4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Additional dark theme enhancements */
        .form-group input::placeholder {
            color: #888888;
        }

        .search-results::-webkit-scrollbar {
            width: 8px;
        }

        .search-results::-webkit-scrollbar-track {
            background: #2E2E2E;
        }

        .search-results::-webkit-scrollbar-thumb {
            background: #555555;
            border-radius: 4px;
        }

        .search-results::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }

        .console::-webkit-scrollbar {
            width: 8px;
        }

        .console::-webkit-scrollbar-track {
            background: #1A1A1A;
        }

        .console::-webkit-scrollbar-thumb {
            background: #333333;
            border-radius: 4px;
        }

        .console::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }

        /* Tab Navigation Styles */
        .tab-navigation {
            background: #2E2E2E;
            border-bottom: 2px solid #4A4A4A;
            display: flex;
            padding: 0;
            margin: 0;
        }

        .tab-button {
            background: #3A3A3A;
            color: #CCCCCC;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            flex: 1;
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .tab-button:hover {
            background: #4A4A4A;
            color: #FFFFFF;
        }

        .tab-button.active {
            background: #00BCD4;
            color: #FFFFFF;
            border-bottom: 3px solid #33B5E5;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Playlist tab specific styles */
        .playlist-container {
            padding: 30px;
            text-align: center;
            color: #CCCCCC;
        }

        .playlist-header {
            color: #00BCD4;
            font-size: 2em;
            margin-bottom: 20px;
        }

        .playlist-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .playlist-section {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #4A4A4A;
        }

        .playlist-section h3 {
            color: #00BCD4;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .playlist-section p {
            color: #CCCCCC;
            line-height: 1.5;
        }

        .coming-soon {
            background: #333333;
            border: 2px dashed #555555;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
        }

        .coming-soon h2 {
            color: #00BCD4;
            margin-bottom: 15px;
        }

        .coming-soon p {
            color: #999999;
            font-size: 1.1em;
        }

        /* Plex Connection Styles */
        .plex-connection {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #4A4A4A;
        }

        .plex-connection h3 {
            color: #00BCD4;
            margin-bottom: 15px;
        }

        .connection-form {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .connection-status {
            padding: 10px 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-weight: bold;
        }

        .connection-status.connected {
            background: #4CAF50;
            color: white;
        }

        .connection-status.disconnected {
            background: #F44336;
            color: white;
        }

        /* Music Library Styles */
        .music-library {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #4A4A4A;
        }

        .library-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .library-header h3 {
            color: #00BCD4;
            margin: 0;
        }

        .library-stats {
            color: #CCCCCC;
            font-size: 0.9em;
        }

        .search-filter {
            margin-bottom: 15px;
        }

        .search-filter input {
            width: 100%;
            padding: 10px;
            border: 2px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
            color: #FFFFFF;
            font-size: 14px;
        }

        .search-filter input:focus {
            outline: none;
            border-color: #00BCD4;
        }

        /* Track Table Styles */
        .track-table-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
        }

        .track-table {
            width: 100%;
            border-collapse: collapse;
        }

        .track-table th {
            background: #333333;
            color: #00BCD4;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .track-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #4A4A4A;
            color: #CCCCCC;
        }

        .track-table tr:hover {
            background: #3A3A3A;
        }

        .track-table tr.selected {
            background: #00BCD4;
            color: #FFFFFF;
        }

        .track-checkbox {
            margin-right: 8px;
        }

        /* Selected Songs Styles */
        .selected-songs {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #4A4A4A;
        }

        .selected-songs h3 {
            color: #00BCD4;
            margin-bottom: 15px;
        }

        .selected-count {
            color: #00BCD4;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .selected-track-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
        }

        .selected-track-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #4A4A4A;
            color: #CCCCCC;
        }

        .selected-track-item:last-child {
            border-bottom: none;
        }

        .selected-track-info {
            flex: 1;
        }

        .selected-track-title {
            font-weight: bold;
            color: #FFFFFF;
        }

        .selected-track-artist {
            font-size: 0.9em;
            color: #CCCCCC;
        }

        .remove-track-btn {
            background: #F44336;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .remove-track-btn:hover {
            background: #D32F2F;
        }

        /* Playlist Creation Styles */
        .playlist-creation {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #4A4A4A;
        }

        .playlist-creation h3 {
            color: #00BCD4;
            margin-bottom: 15px;
        }

        .playlist-form {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .playlist-name-input {
            flex: 1;
            min-width: 200px;
        }

        .playlist-name-input input {
            width: 100%;
            padding: 12px;
            border: 2px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
            color: #FFFFFF;
            font-size: 16px;
        }

        .playlist-name-input input:focus {
            outline: none;
            border-color: #00BCD4;
        }

        .playlist-actions {
            display: flex;
            gap: 10px;
        }

        .btn.secondary {
            background: #555555;
            border: 1px solid #555555;
        }

        .btn.secondary:hover {
            background: #666666;
            box-shadow: 0 5px 15px rgba(85, 85, 85, 0.3);
        }

        /* Loading and Message Styles */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 10px;
            z-index: 100;
        }

        .loading-overlay.hidden {
            display: none;
        }

        .message {
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .message.success {
            background: #4CAF50;
            color: white;
        }

        .message.error {
            background: #F44336;
            color: white;
        }

        .message.info {
            background: #00BCD4;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Harmonix</h1>
            <p>Download your Spotify library using Deemix with real-time progress tracking</p>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-button active" onclick="switchTab('download')">Download Music</button>
            <button class="tab-button" onclick="switchTab('playlists')">Playlists</button>
        </div>

        <!-- Download Music Tab Content -->
        <div id="download-tab" class="tab-content active">
            <div class="controls">
            <button id="startBtn" class="btn">
                <span id="startText">Start Download</span>
            </button>
            <button id="stopBtn" class="btn stop">
                Stop Download
            </button>
        </div>

        <div class="manual-search">
            <h2>🔍 Manual Track Search</h2>
            <div class="search-form">
                <div class="form-group">
                    <label for="artistInput">Artist:</label>
                    <input type="text" id="artistInput" placeholder="Enter artist name">
                </div>
                <div class="form-group">
                    <label for="albumInput">Album:</label>
                    <input type="text" id="albumInput" placeholder="Enter album name">
                </div>
                <div class="form-group">
                    <label for="trackInput">Track:</label>
                    <input type="text" id="trackInput" placeholder="Enter track name">
                </div>
                <div class="form-group">
                    <button id="searchBtn" class="btn">Search</button>
                </div>
            </div>
            <div id="searchResults" class="search-results hidden">
                <!-- Search results will be populated here -->
            </div>
        </div>

        <div class="status">
            <span id="statusIndicator" class="status-indicator"></span>
            <span id="statusText">Ready to start download</span>
        </div>

            <div class="console" id="console">
                <div class="console-line timestamp">Console output will appear here...</div>
            </div>
        </div>

        <!-- Playlists Tab Content -->
        <div id="playlists-tab" class="tab-content">
            <div class="playlist-container">
                <h2 class="playlist-header">🎵 Playlist Management</h2>

                <!-- Plex Connection Section -->
                <div class="plex-connection">
                    <h3>🔗 Plex Server Connection</h3>
                    <div class="connection-form">
                        <div class="form-group">
                            <label for="plexUrl">Plex Server URL:</label>
                            <input type="text" id="plexUrl" placeholder="http://192.168.1.100:32400">
                        </div>
                        <div class="form-group">
                            <label for="plexToken">Plex Token:</label>
                            <input type="text" id="plexToken" placeholder="Your Plex authentication token">
                        </div>
                        <div class="form-group">
                            <button id="connectPlexBtn" class="btn">Connect</button>
                        </div>
                    </div>
                    <div id="connectionStatus" class="connection-status disconnected">
                        Not connected to Plex server
                    </div>
                </div>

                <!-- Music Library Section -->
                <div id="musicLibrarySection" class="music-library" style="display: none;">
                    <div class="library-header">
                        <h3>🎵 Music Library</h3>
                        <div class="library-stats">
                            <span id="libraryStats">Loading...</span>
                        </div>
                    </div>

                    <div class="search-filter">
                        <input type="text" id="trackSearch" placeholder="Search tracks by title, artist, or album...">
                    </div>

                    <div class="track-table-container">
                        <table class="track-table">
                            <thead>
                                <tr>
                                    <th width="40px">
                                        <input type="checkbox" id="selectAllTracks" class="track-checkbox">
                                    </th>
                                    <th>Title</th>
                                    <th>Artist</th>
                                    <th>Album</th>
                                    <th>Duration</th>
                                </tr>
                            </thead>
                            <tbody id="trackTableBody">
                                <!-- Tracks will be populated here -->
                            </tbody>
                        </table>
                    </div>
                    <div id="libraryLoading" class="loading-overlay">
                        <div class="loading"></div>
                    </div>
                </div>

                <!-- Selected Songs Section -->
                <div id="selectedSongsSection" class="selected-songs" style="display: none;">
                    <h3>📋 Selected Songs</h3>
                    <div class="selected-count">
                        <span id="selectedCount">0 songs selected</span>
                    </div>
                    <div class="selected-track-list" id="selectedTrackList">
                        <!-- Selected tracks will be populated here -->
                    </div>
                    <div style="margin-top: 15px;">
                        <button id="clearSelectionBtn" class="btn secondary">Clear Selection</button>
                    </div>
                </div>

                <!-- Playlist Creation Section -->
                <div id="playlistCreationSection" class="playlist-creation" style="display: none;">
                    <h3>📝 Create Playlist</h3>
                    <div class="playlist-form">
                        <div class="playlist-name-input">
                            <label for="playlistName">Playlist Name:</label>
                            <input type="text" id="playlistName" placeholder="Enter playlist name">
                        </div>
                        <div class="playlist-actions">
                            <button id="createPlaylistBtn" class="btn">Create Playlist</button>
                        </div>
                    </div>
                    <div id="playlistMessages"></div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Harmonix Web Interface | Real-time console output</p>
        </div>
    </div>

    <script>
        const socket = io();
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const startText = document.getElementById('startText');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const console = document.getElementById('console');
        const searchBtn = document.getElementById('searchBtn');
        const artistInput = document.getElementById('artistInput');
        const albumInput = document.getElementById('albumInput');
        const trackInput = document.getElementById('trackInput');
        const searchResults = document.getElementById('searchResults');

        // Plex-related elements
        const connectPlexBtn = document.getElementById('connectPlexBtn');
        const plexUrl = document.getElementById('plexUrl');
        const plexToken = document.getElementById('plexToken');
        const connectionStatus = document.getElementById('connectionStatus');
        const musicLibrarySection = document.getElementById('musicLibrarySection');
        const selectedSongsSection = document.getElementById('selectedSongsSection');
        const playlistCreationSection = document.getElementById('playlistCreationSection');
        const trackTableBody = document.getElementById('trackTableBody');
        const trackSearch = document.getElementById('trackSearch');
        const selectAllTracks = document.getElementById('selectAllTracks');
        const selectedTrackList = document.getElementById('selectedTrackList');
        const selectedCount = document.getElementById('selectedCount');
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        const createPlaylistBtn = document.getElementById('createPlaylistBtn');
        const playlistName = document.getElementById('playlistName');
        const playlistMessages = document.getElementById('playlistMessages');
        const libraryStats = document.getElementById('libraryStats');
        const libraryLoading = document.getElementById('libraryLoading');

        let isRunning = false;
        let plexConnected = false;
        let allTracks = [];
        let filteredTracks = [];
        let selectedTracks = new Map();

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Show selected tab content
            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Add active class to clicked tab button
            const clickedButton = event.target;
            clickedButton.classList.add('active');
        }

        function addConsoleMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }

        function updateStatus(running, message) {
            isRunning = running;
            statusText.textContent = message;

            if (running) {
                statusIndicator.className = 'status-indicator running';
                startBtn.disabled = true;
                stopBtn.disabled = false;
                stopBtn.classList.add('visible');
                startText.innerHTML = '<span class="loading"></span>Running...';
            } else {
                statusIndicator.className = 'status-indicator';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                stopBtn.classList.remove('visible');
                startText.textContent = 'Start Download';
            }
        }

        // Socket event handlers
        socket.on('connected', (data) => {
            addConsoleMessage('Connected to server', 'success');
        });

        socket.on('script_started', (data) => {
            updateStatus(true, 'Download in progress...');
            addConsoleMessage(data.message, 'info');
        });

        socket.on('script_output', (data) => {
            let type = 'info';
            if (data.message.includes('✅')) type = 'success';
            else if (data.message.includes('❌')) type = 'error';
            else if (data.message.includes('⚠️')) type = 'warning';
            else if (data.message.includes('⏭️')) type = 'warning';
            
            addConsoleMessage(data.message, type);
        });

        socket.on('script_completed', (data) => {
            updateStatus(false, data.success ? 'Download completed successfully' : 'Download failed');
            addConsoleMessage(data.message, data.success ? 'success' : 'error');
        });

        socket.on('script_error', (data) => {
            updateStatus(false, 'Error occurred');
            statusIndicator.className = 'status-indicator error';
            addConsoleMessage(data.message, 'error');
        });

        socket.on('download_started', (data) => {
            addConsoleMessage(data.message, 'info');
        });

        socket.on('download_stopped', (data) => {
            updateStatus(false, 'Download stopped');
            addConsoleMessage(data.message, 'warning');
        });

        socket.on('error', (data) => {
            addConsoleMessage(data.message, 'error');
        });

        // Search functionality
        function performSearch() {
            const artist = artistInput.value.trim();
            const album = albumInput.value.trim();
            const track = trackInput.value.trim();

            if (!artist && !album && !track) {
                addConsoleMessage('Please enter at least an artist, album, or track name', 'warning');
                return;
            }

            const query = `${artist} ${album} ${track}`.trim();
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<span class="loading"></span>Searching...';

            addConsoleMessage(`Searching for: ${query}`, 'info');

            fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';

                if (data.success) {
                    displaySearchResults(data.results);
                    addConsoleMessage(`Found ${data.results.length} results`, 'success');
                } else {
                    addConsoleMessage(`Search failed: ${data.error}`, 'error');
                    searchResults.classList.add('hidden');
                }
            })
            .catch(error => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';
                addConsoleMessage(`Search error: ${error.message}`, 'error');
                searchResults.classList.add('hidden');
            });
        }

        function displaySearchResults(results) {
            searchResults.innerHTML = '';

            if (results.length === 0) {
                searchResults.innerHTML = '<div class="result-item">No results found</div>';
            } else {
                results.forEach((result, index) => {
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item';
                    resultItem.innerHTML = `
                        <div class="result-info">
                            <div class="result-title">${result.title}</div>
                            <div class="result-artist">by ${result.artist}</div>
                            <div class="result-album">Album: ${result.album}</div>
                        </div>
                        <button class="btn small" onclick="downloadTrack('${result.id}', '${result.title}', '${result.artist}')">
                            Download
                        </button>
                    `;
                    searchResults.appendChild(resultItem);
                });
            }

            searchResults.classList.remove('hidden');
        }

        function downloadTrack(trackId, title, artist) {
            const trackUrl = `https://www.deezer.com/track/${trackId}`;
            addConsoleMessage(`Starting download: ${title} by ${artist}`, 'info');

            fetch('/download_track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    track_url: trackUrl,
                    title: title,
                    artist: artist
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addConsoleMessage(data.message, 'success');
                } else {
                    addConsoleMessage(`Download failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                addConsoleMessage(`Download error: ${error.message}`, 'error');
            });
        }

        // Plex functionality
        function connectToPlex() {
            const url = plexUrl.value.trim();
            const token = plexToken.value.trim();

            if (!url || !token) {
                showMessage('Please enter both Plex URL and token', 'error');
                return;
            }

            connectPlexBtn.disabled = true;
            connectPlexBtn.innerHTML = '<span class="loading"></span>Connecting...';

            fetch('/plex/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url, token: token })
            })
            .then(response => response.json())
            .then(data => {
                connectPlexBtn.disabled = false;
                connectPlexBtn.textContent = 'Connect';

                if (data.success) {
                    plexConnected = true;
                    connectionStatus.textContent = `Connected to: ${data.server_name}`;
                    connectionStatus.className = 'connection-status connected';
                    showMessage(data.message, 'success');
                    loadMusicLibrary();
                } else {
                    connectionStatus.textContent = `Connection failed: ${data.error}`;
                    connectionStatus.className = 'connection-status disconnected';
                    showMessage(data.error, 'error');
                }
            })
            .catch(error => {
                connectPlexBtn.disabled = false;
                connectPlexBtn.textContent = 'Connect';
                showMessage(`Connection error: ${error.message}`, 'error');
            });
        }

        function loadMusicLibrary() {
            if (!plexConnected) return;

            musicLibrarySection.style.display = 'block';
            selectedSongsSection.style.display = 'block';
            playlistCreationSection.style.display = 'block';
            libraryLoading.classList.remove('hidden');

            fetch('/plex/music')
            .then(response => response.json())
            .then(data => {
                libraryLoading.classList.add('hidden');

                if (data.success) {
                    allTracks = data.tracks;
                    filteredTracks = [...allTracks];
                    displayTracks(filteredTracks);
                    libraryStats.textContent = `${data.total} tracks from ${data.library_name}`;
                } else {
                    showMessage(data.error, 'error');
                }
            })
            .catch(error => {
                libraryLoading.classList.add('hidden');
                showMessage(`Failed to load library: ${error.message}`, 'error');
            });
        }

        function displayTracks(tracks) {
            trackTableBody.innerHTML = '';

            tracks.forEach(track => {
                const row = document.createElement('tr');
                const isSelected = selectedTracks.has(track.id);

                if (isSelected) {
                    row.classList.add('selected');
                }

                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="track-checkbox" data-track-id="${track.id}" ${isSelected ? 'checked' : ''}>
                    </td>
                    <td>${track.title}</td>
                    <td>${track.artist}</td>
                    <td>${track.album}</td>
                    <td>${formatDuration(track.duration)}</td>
                `;

                const checkbox = row.querySelector('.track-checkbox');
                checkbox.addEventListener('change', () => toggleTrackSelection(track, checkbox.checked));

                trackTableBody.appendChild(row);
            });
        }

        function toggleTrackSelection(track, selected) {
            if (selected) {
                selectedTracks.set(track.id, track);
            } else {
                selectedTracks.delete(track.id);
            }
            updateSelectedDisplay();
            updateSelectAllCheckbox();
        }

        function updateSelectedDisplay() {
            selectedCount.textContent = `${selectedTracks.size} songs selected`;

            selectedTrackList.innerHTML = '';
            selectedTracks.forEach(track => {
                const item = document.createElement('div');
                item.className = 'selected-track-item';
                item.innerHTML = `
                    <div class="selected-track-info">
                        <div class="selected-track-title">${track.title}</div>
                        <div class="selected-track-artist">${track.artist} - ${track.album}</div>
                    </div>
                    <button class="remove-track-btn" onclick="removeTrackFromSelection('${track.id}')">Remove</button>
                `;
                selectedTrackList.appendChild(item);
            });
        }

        function removeTrackFromSelection(trackId) {
            selectedTracks.delete(trackId);
            updateSelectedDisplay();

            // Update checkbox in table
            const checkbox = document.querySelector(`input[data-track-id="${trackId}"]`);
            if (checkbox) {
                checkbox.checked = false;
                checkbox.closest('tr').classList.remove('selected');
            }
            updateSelectAllCheckbox();
        }

        function updateSelectAllCheckbox() {
            const visibleCheckboxes = document.querySelectorAll('.track-checkbox[data-track-id]');
            const checkedCount = document.querySelectorAll('.track-checkbox[data-track-id]:checked').length;

            selectAllTracks.indeterminate = checkedCount > 0 && checkedCount < visibleCheckboxes.length;
            selectAllTracks.checked = checkedCount === visibleCheckboxes.length && visibleCheckboxes.length > 0;
        }

        function filterTracks() {
            const searchTerm = trackSearch.value.toLowerCase();
            filteredTracks = allTracks.filter(track =>
                track.title.toLowerCase().includes(searchTerm) ||
                track.artist.toLowerCase().includes(searchTerm) ||
                track.album.toLowerCase().includes(searchTerm)
            );
            displayTracks(filteredTracks);
        }

        function clearSelection() {
            selectedTracks.clear();
            updateSelectedDisplay();

            // Uncheck all checkboxes
            document.querySelectorAll('.track-checkbox[data-track-id]').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.closest('tr').classList.remove('selected');
            });
            updateSelectAllCheckbox();
        }

        function createPlaylist() {
            const name = playlistName.value.trim();

            if (!name) {
                showMessage('Please enter a playlist name', 'error');
                return;
            }

            if (selectedTracks.size === 0) {
                showMessage('Please select at least one track', 'error');
                return;
            }

            createPlaylistBtn.disabled = true;
            createPlaylistBtn.innerHTML = '<span class="loading"></span>Creating...';

            const trackIds = Array.from(selectedTracks.keys());

            fetch('/plex/playlist/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    track_ids: trackIds
                })
            })
            .then(response => response.json())
            .then(data => {
                createPlaylistBtn.disabled = false;
                createPlaylistBtn.textContent = 'Create Playlist';

                if (data.success) {
                    showMessage(data.message, 'success');
                    playlistName.value = '';
                    clearSelection();
                } else {
                    showMessage(data.error, 'error');
                }
            })
            .catch(error => {
                createPlaylistBtn.disabled = false;
                createPlaylistBtn.textContent = 'Create Playlist';
                showMessage(`Playlist creation failed: ${error.message}`, 'error');
            });
        }

        function formatDuration(seconds) {
            if (!seconds) return '--:--';
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;

            playlistMessages.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // Button event handlers
        startBtn.addEventListener('click', () => {
            if (!isRunning) {
                socket.emit('start_download');
                addConsoleMessage('Requesting download start...', 'info');
            }
        });

        stopBtn.addEventListener('click', () => {
            if (isRunning) {
                socket.emit('stop_download');
                addConsoleMessage('Requesting download stop...', 'warning');
            }
        });

        searchBtn.addEventListener('click', performSearch);

        // Enter key support for search inputs
        artistInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        albumInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        trackInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        // Plex event listeners
        connectPlexBtn.addEventListener('click', connectToPlex);
        trackSearch.addEventListener('input', filterTracks);
        clearSelectionBtn.addEventListener('click', clearSelection);
        createPlaylistBtn.addEventListener('click', createPlaylist);

        selectAllTracks.addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.track-checkbox[data-track-id]');
            checkboxes.forEach(checkbox => {
                const trackId = checkbox.dataset.trackId;
                const track = filteredTracks.find(t => t.id === trackId);
                if (track) {
                    checkbox.checked = e.target.checked;
                    toggleTrackSelection(track, e.target.checked);
                    checkbox.closest('tr').classList.toggle('selected', e.target.checked);
                }
            });
        });

        // Initial status check
        fetch('/status')
            .then(response => response.json())
            .then(data => {
                updateStatus(data.running, data.running ? 'Download in progress...' : 'Ready to start download');
            })
            .catch(error => {
                addConsoleMessage('Failed to get initial status', 'error');
            });
    </script>
</body>
</html>
