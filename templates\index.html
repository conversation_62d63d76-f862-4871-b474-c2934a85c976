<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Harmonix</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #2E2E2E;
            min-height: 100vh;
            padding: 20px;
            color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #3A3A3A;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            border: 1px solid #4A4A4A;
        }

        .header {
            background: #2E2E2E;
            color: #FFFFFF;
            padding: 30px;
            text-align: center;
            border-bottom: 2px solid #00BCD4;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #00BCD4;
        }

        .header p {
            font-size: 1.1em;
            color: #CCCCCC;
        }

        .controls {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #4A4A4A;
            background: #3A3A3A;
        }

        .manual-search {
            padding: 30px;
            border-bottom: 1px solid #4A4A4A;
            background: #333333;
        }

        .manual-search h2 {
            margin-bottom: 20px;
            color: #00BCD4;
            text-align: center;
        }

        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
            justify-content: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #CCCCCC;
        }

        .form-group input {
            padding: 12px;
            border: 2px solid #555555;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .form-group input:focus {
            outline: none;
            border-color: #00BCD4;
            box-shadow: 0 0 5px rgba(0, 188, 212, 0.3);
        }

        .search-results {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 8px;
            background: #2E2E2E;
        }

        .search-results.hidden {
            display: none;
        }

        .result-item {
            padding: 15px;
            border-bottom: 1px solid #4A4A4A;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .result-item:hover {
            background-color: #3A3A3A;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-info {
            flex: 1;
        }

        .result-title {
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 5px;
        }

        .result-artist {
            color: #CCCCCC;
            font-size: 14px;
        }

        .result-album {
            color: #999999;
            font-size: 12px;
            margin-top: 2px;
        }

        .btn.small {
            padding: 8px 16px;
            font-size: 14px;
            min-width: auto;
        }

        .btn {
            background: linear-gradient(135deg, #00BCD4 0%, #33B5E5 100%);
            color: #FFFFFF;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            min-width: 150px;
            border: 1px solid #00BCD4;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 188, 212, 0.3);
            background: linear-gradient(135deg, #33B5E5 0%, #00BCD4 100%);
        }

        .btn:disabled {
            background: #555555;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            border: 1px solid #555555;
            color: #999999;
            text-shadow: none;
        }

        .btn.stop {
            background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
            border: 1px solid #F44336;
            display: none;
        }

        .btn.stop.visible {
            display: inline-block;
        }

        .btn.stop:hover {
            background: linear-gradient(135deg, #D32F2F 0%, #F44336 100%);
            box-shadow: 0 10px 20px rgba(244, 67, 54, 0.3);
        }

        .status {
            padding: 20px 30px;
            background: #333333;
            border-bottom: 1px solid #4A4A4A;
            color: #CCCCCC;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            background: #4CAF50;
        }

        .status-indicator.running {
            background: #00BCD4;
            animation: pulse 1.5s infinite;
        }

        .status-indicator.error {
            background: #F44336;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .console {
            background: #1A1A1A;
            color: #00FF00;
            font-family: 'Courier New', monospace;
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            font-size: 14px;
            line-height: 1.4;
            border: 1px solid #333333;
        }

        .console-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .console-line.success {
            color: #4CAF50;
        }

        .console-line.error {
            color: #F44336;
        }

        .console-line.warning {
            color: #FF9800;
        }

        .console-line.info {
            color: #00BCD4;
        }

        .console-line.timestamp {
            color: #777777;
            font-size: 12px;
        }

        .footer {
            padding: 20px 30px;
            text-align: center;
            color: #999999;
            font-size: 0.9em;
            background: #2E2E2E;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #555555;
            border-top: 3px solid #00BCD4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Additional dark theme enhancements */
        .form-group input::placeholder {
            color: #888888;
        }

        .search-results::-webkit-scrollbar {
            width: 8px;
        }

        .search-results::-webkit-scrollbar-track {
            background: #2E2E2E;
        }

        .search-results::-webkit-scrollbar-thumb {
            background: #555555;
            border-radius: 4px;
        }

        .search-results::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }

        .console::-webkit-scrollbar {
            width: 8px;
        }

        .console::-webkit-scrollbar-track {
            background: #1A1A1A;
        }

        .console::-webkit-scrollbar-thumb {
            background: #333333;
            border-radius: 4px;
        }

        .console::-webkit-scrollbar-thumb:hover {
            background: #00BCD4;
        }

        /* Tab Navigation Styles */
        .tab-navigation {
            background: #2E2E2E;
            border-bottom: 2px solid #4A4A4A;
            display: flex;
            padding: 0;
            margin: 0;
        }

        .tab-button {
            background: #3A3A3A;
            color: #CCCCCC;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            flex: 1;
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .tab-button:hover {
            background: #4A4A4A;
            color: #FFFFFF;
        }

        .tab-button.active {
            background: #00BCD4;
            color: #FFFFFF;
            border-bottom: 3px solid #33B5E5;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Playlist tab specific styles */
        .playlist-container {
            padding: 30px;
            text-align: center;
            color: #CCCCCC;
        }

        .playlist-header {
            color: #00BCD4;
            font-size: 2em;
            margin-bottom: 20px;
        }

        .playlist-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .playlist-section {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #4A4A4A;
        }

        .playlist-section h3 {
            color: #00BCD4;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .playlist-section p {
            color: #CCCCCC;
            line-height: 1.5;
        }

        .coming-soon {
            background: #333333;
            border: 2px dashed #555555;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
        }

        .coming-soon h2 {
            color: #00BCD4;
            margin-bottom: 15px;
        }

        .coming-soon p {
            color: #999999;
            font-size: 1.1em;
        }

        /* Plex Connection Styles */
        .plex-connection {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #4A4A4A;
        }

        .plex-connection.connected {
            border-color: #4CAF50;
            background: #2E4A2E;
        }

        .plex-connection.disconnected {
            border-color: #F44336;
            background: #4A2E2E;
        }

        .connection-form {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #F44336;
        }

        .status-dot.connected {
            background: #4CAF50;
        }

        /* Music Library Styles */
        .music-library {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .library-section {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #4A4A4A;
        }

        .library-section h3 {
            color: #00BCD4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-library {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .search-library input {
            flex: 1;
            padding: 10px;
            border: 2px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .track-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
        }

        .track-item {
            padding: 10px;
            border-bottom: 1px solid #4A4A4A;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: background-color 0.2s ease;
        }

        .track-item:hover {
            background-color: #3A3A3A;
        }

        .track-item:last-child {
            border-bottom: none;
        }

        .track-item.selected {
            background-color: #004A5A;
        }

        .track-checkbox {
            margin-right: 10px;
        }

        .track-info {
            flex: 1;
        }

        .track-title {
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 2px;
        }

        .track-details {
            font-size: 12px;
            color: #CCCCCC;
        }

        .track-duration {
            color: #999999;
            font-size: 12px;
        }

        /* Selected Songs Styles */
        .selected-songs {
            min-height: 200px;
        }

        .selected-track {
            padding: 8px;
            background: #004A5A;
            border-radius: 5px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: move;
        }

        .selected-track:hover {
            background: #005A6A;
        }

        .remove-track {
            background: #F44336;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 12px;
        }

        .remove-track:hover {
            background: #D32F2F;
        }

        /* Playlist Creation Styles */
        .playlist-creation {
            background: #333333;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #4A4A4A;
        }

        .playlist-form {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 15px;
        }

        .playlist-form input {
            flex: 1;
            padding: 12px;
            border: 2px solid #555555;
            border-radius: 5px;
            background: #2E2E2E;
            color: #FFFFFF;
        }

        .selection-counter {
            color: #00BCD4;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .playlist-actions {
            display: flex;
            gap: 10px;
        }

        .btn.secondary {
            background: #555555;
            border: 1px solid #666666;
        }

        .btn.secondary:hover {
            background: #666666;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            z-index: 10;
        }

        .loading-overlay.hidden {
            display: none;
        }

        .drag-over {
            border: 2px dashed #00BCD4;
            background: rgba(0, 188, 212, 0.1);
        }

        /* AI Playlist Suggestions Styles */
        .suggestions-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .suggestions-header h2 {
            color: #00BCD4;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .suggestions-header p {
            color: #CCCCCC;
            font-size: 1.1em;
        }

        .refresh-suggestions {
            text-align: center;
            margin-bottom: 30px;
        }

        .playlist-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            padding: 20px;
        }

        .playlist-card {
            background: #3A3A3A;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #4A4A4A;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .playlist-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 188, 212, 0.2);
            border-color: #00BCD4;
        }

        .playlist-header {
            margin-bottom: 20px;
        }

        .playlist-title {
            color: #00BCD4;
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .playlist-description {
            color: #CCCCCC;
            font-size: 0.95em;
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .playlist-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .playlist-tag {
            background: #555555;
            color: #FFFFFF;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            border: 1px solid #666666;
        }

        .playlist-tag.genre {
            background: #4A90E2;
            border-color: #5BA0F2;
        }

        .playlist-tag.decade {
            background: #E24A90;
            border-color: #F25BA0;
        }

        .playlist-tag.tempo {
            background: #90E24A;
            border-color: #A0F25B;
        }

        .playlist-tag.mood {
            background: #E2904A;
            border-color: #F2A05B;
        }

        .playlist-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background: #2E2E2E;
            border-radius: 8px;
        }

        .track-count {
            color: #00BCD4;
            font-weight: bold;
        }

        .duration {
            color: #CCCCCC;
            font-size: 0.9em;
        }

        .track-preview {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid #555555;
            border-radius: 8px;
            background: #2E2E2E;
        }

        .track-preview-item {
            padding: 8px 12px;
            border-bottom: 1px solid #4A4A4A;
            font-size: 0.85em;
        }

        .track-preview-item:last-child {
            border-bottom: none;
        }

        .track-preview-title {
            color: #FFFFFF;
            font-weight: bold;
        }

        .track-preview-artist {
            color: #CCCCCC;
            margin-top: 2px;
        }

        .playlist-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .download-btn {
            flex: 1;
            background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
            color: #FFFFFF;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            border: 1px solid #4CAF50;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
            background: linear-gradient(135deg, #45A049 0%, #4CAF50 100%);
        }

        .download-btn:disabled {
            background: #555555;
            border-color: #555555;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .preview-btn {
            background: #555555;
            color: #FFFFFF;
            border: 1px solid #666666;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preview-btn:hover {
            background: #666666;
        }

        .playlist-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-checking {
            background: #FF9800;
            color: #FFFFFF;
        }

        .status-ready {
            background: #4CAF50;
            color: #FFFFFF;
        }

        .status-downloading {
            background: #2196F3;
            color: #FFFFFF;
        }

        .status-completed {
            background: #4CAF50;
            color: #FFFFFF;
        }

        .status-error {
            background: #F44336;
            color: #FFFFFF;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #555555;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00BCD4, #4CAF50);
            width: 0%;
            transition: width 0.3s ease;
        }

        .missing-tracks-info {
            background: #2E4A2E;
            border: 1px solid #4CAF50;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .missing-tracks-info.no-missing {
            background: #4A2E2E;
            border-color: #F44336;
        }

        .missing-count {
            color: #4CAF50;
            font-weight: bold;
        }

        .missing-count.none {
            color: #F44336;
        }

        /* Connection status for AI suggestions */
        .plex-connection-status {
            background: #3A3A3A;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid #4A4A4A;
        }

        .plex-connection-status.connected {
            border-color: #4CAF50;
            background: #2E4A2E;
        }

        .plex-connection-status.disconnected {
            border-color: #F44336;
            background: #4A2E2E;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Harmonix</h1>
            <p>Download your Spotify library using Deemix with real-time progress tracking</p>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-button active" onclick="switchTab('download')">Download Music</button>
            <button class="tab-button" onclick="switchTab('playlists')">Playlists</button>
        </div>

        <!-- Download Music Tab Content -->
        <div id="download-tab" class="tab-content active">
            <div class="controls">
            <button id="startBtn" class="btn">
                <span id="startText">Start Download</span>
            </button>
            <button id="stopBtn" class="btn stop">
                Stop Download
            </button>
        </div>

        <div class="manual-search">
            <h2>🔍 Manual Track Search</h2>
            <div class="search-form">
                <div class="form-group">
                    <label for="artistInput">Artist:</label>
                    <input type="text" id="artistInput" placeholder="Enter artist name">
                </div>
                <div class="form-group">
                    <label for="albumInput">Album:</label>
                    <input type="text" id="albumInput" placeholder="Enter album name">
                </div>
                <div class="form-group">
                    <label for="trackInput">Track:</label>
                    <input type="text" id="trackInput" placeholder="Enter track name">
                </div>
                <div class="form-group">
                    <button id="searchBtn" class="btn">Search</button>
                </div>
            </div>
            <div id="searchResults" class="search-results hidden">
                <!-- Search results will be populated here -->
            </div>
        </div>

        <div class="status">
            <span id="statusIndicator" class="status-indicator"></span>
            <span id="statusText">Ready to start download</span>
        </div>

            <div class="console" id="console">
                <div class="console-line timestamp">Console output will appear here...</div>
            </div>
        </div>

        <!-- Playlists Tab Content -->
        <div id="playlists-tab" class="tab-content">
            <div class="playlist-container">
                <!-- Header Section -->
                <div class="suggestions-header">
                    <h2>🎵 AI Playlist Suggestions</h2>
                    <p>Discover new music with curated playlists tailored to different genres, decades, and moods</p>
                </div>

                <!-- Plex Connection Status -->
                <div id="plexConnectionStatus" class="plex-connection-status disconnected">
                    <div class="connection-status">
                        <div id="connectionDot" class="status-dot"></div>
                        <span id="connectionStatusText">Not connected to Plex server</span>
                    </div>
                    <p style="margin-top: 10px; color: #CCCCCC;">
                        Connect to Plex in the Download Music tab to enable automatic playlist creation
                    </p>
                </div>

                <!-- Refresh Suggestions -->
                <div class="refresh-suggestions">
                    <button id="refreshSuggestionsBtn" class="btn">
                        🔄 Generate New Suggestions
                    </button>
                    <button id="checkAllLibrariesBtn" class="btn secondary" style="margin-left: 15px;" disabled>
                        📚 Check All Against Library
                    </button>
                    <button id="debugLibraryBtn" class="btn secondary" style="margin-left: 15px;" disabled>
                        🔍 Debug Library
                    </button>
                    <button id="testDownloadBtn" class="btn secondary" style="margin-left: 15px;">
                        🧪 Test Download
                    </button>
                </div>

                <!-- Loading State -->
                <div id="suggestionsLoading" class="loading-overlay" style="position: relative; height: 100px;">
                    <div class="loading"></div>
                </div>

                <!-- Playlist Suggestions Grid -->
                <div id="playlistGrid" class="playlist-grid" style="display: none;">
                    <!-- Playlist cards will be dynamically generated here -->
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Harmonix Web Interface | Real-time console output</p>
        </div>
    </div>

    <script>
        const socket = io();
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const startText = document.getElementById('startText');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const console = document.getElementById('console');
        const searchBtn = document.getElementById('searchBtn');
        const artistInput = document.getElementById('artistInput');
        const albumInput = document.getElementById('albumInput');
        const trackInput = document.getElementById('trackInput');
        const searchResults = document.getElementById('searchResults');

        // AI Playlist Suggestions elements
        const plexConnectionStatus = document.getElementById('plexConnectionStatus');
        const connectionDot = document.getElementById('connectionDot');
        const connectionStatusText = document.getElementById('connectionStatusText');
        const refreshSuggestionsBtn = document.getElementById('refreshSuggestionsBtn');
        const checkAllLibrariesBtn = document.getElementById('checkAllLibrariesBtn');
        const debugLibraryBtn = document.getElementById('debugLibraryBtn');
        const testDownloadBtn = document.getElementById('testDownloadBtn');
        const suggestionsLoading = document.getElementById('suggestionsLoading');
        const playlistGrid = document.getElementById('playlistGrid');

        let isRunning = false;
        let currentSuggestions = [];
        let plexConnected = false;

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Show selected tab content
            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Add active class to clicked tab button
            const clickedButton = event.target;
            clickedButton.classList.add('active');
        }

        function addConsoleMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }

        function updateStatus(running, message) {
            isRunning = running;
            statusText.textContent = message;

            if (running) {
                statusIndicator.className = 'status-indicator running';
                startBtn.disabled = true;
                stopBtn.disabled = false;
                stopBtn.classList.add('visible');
                startText.innerHTML = '<span class="loading"></span>Running...';
            } else {
                statusIndicator.className = 'status-indicator';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                stopBtn.classList.remove('visible');
                startText.textContent = 'Start Download';
            }
        }

        // Socket event handlers
        socket.on('connected', (data) => {
            addConsoleMessage('Connected to server', 'success');
        });

        socket.on('script_started', (data) => {
            updateStatus(true, 'Download in progress...');
            addConsoleMessage(data.message, 'info');
        });

        socket.on('script_output', (data) => {
            let type = 'info';
            if (data.message.includes('✅')) type = 'success';
            else if (data.message.includes('❌')) type = 'error';
            else if (data.message.includes('⚠️')) type = 'warning';
            else if (data.message.includes('⏭️')) type = 'warning';
            
            addConsoleMessage(data.message, type);
        });

        socket.on('script_completed', (data) => {
            updateStatus(false, data.success ? 'Download completed successfully' : 'Download failed');
            addConsoleMessage(data.message, data.success ? 'success' : 'error');
        });

        socket.on('script_error', (data) => {
            updateStatus(false, 'Error occurred');
            statusIndicator.className = 'status-indicator error';
            addConsoleMessage(data.message, 'error');
        });

        socket.on('download_started', (data) => {
            addConsoleMessage(data.message, 'info');
        });

        socket.on('download_stopped', (data) => {
            updateStatus(false, 'Download stopped');
            addConsoleMessage(data.message, 'warning');
        });

        socket.on('error', (data) => {
            addConsoleMessage(data.message, 'error');
        });

        // Search functionality
        function performSearch() {
            const artist = artistInput.value.trim();
            const album = albumInput.value.trim();
            const track = trackInput.value.trim();

            if (!artist && !album && !track) {
                addConsoleMessage('Please enter at least an artist, album, or track name', 'warning');
                return;
            }

            const query = `${artist} ${album} ${track}`.trim();
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<span class="loading"></span>Searching...';

            addConsoleMessage(`Searching for: ${query}`, 'info');

            fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';

                if (data.success) {
                    displaySearchResults(data.results);
                    addConsoleMessage(`Found ${data.results.length} results`, 'success');
                } else {
                    addConsoleMessage(`Search failed: ${data.error}`, 'error');
                    searchResults.classList.add('hidden');
                }
            })
            .catch(error => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';
                addConsoleMessage(`Search error: ${error.message}`, 'error');
                searchResults.classList.add('hidden');
            });
        }

        function displaySearchResults(results) {
            searchResults.innerHTML = '';

            if (results.length === 0) {
                searchResults.innerHTML = '<div class="result-item">No results found</div>';
            } else {
                results.forEach((result, index) => {
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item';
                    resultItem.innerHTML = `
                        <div class="result-info">
                            <div class="result-title">${result.title}</div>
                            <div class="result-artist">by ${result.artist}</div>
                            <div class="result-album">Album: ${result.album}</div>
                        </div>
                        <button class="btn small" onclick="downloadTrack('${result.id}', '${result.title}', '${result.artist}')">
                            Download
                        </button>
                    `;
                    searchResults.appendChild(resultItem);
                });
            }

            searchResults.classList.remove('hidden');
        }

        function downloadTrack(trackId, title, artist) {
            const trackUrl = `https://www.deezer.com/track/${trackId}`;
            addConsoleMessage(`Starting download: ${title} by ${artist}`, 'info');

            fetch('/download_track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    track_url: trackUrl,
                    title: title,
                    artist: artist
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addConsoleMessage(data.message, 'success');
                } else {
                    addConsoleMessage(`Download failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                addConsoleMessage(`Download error: ${error.message}`, 'error');
            });
        }

        // AI Playlist Suggestions functionality
        function checkPlexConnection() {
            fetch('/plex/status')
                .then(response => response.json())
                .then(data => {
                    updatePlexConnectionStatus(data.connected, data.server_name);
                })
                .catch(error => {
                    console.error('Error checking Plex status:', error);
                    updatePlexConnectionStatus(false);
                });
        }

        function updatePlexConnectionStatus(connected, serverName = '') {
            plexConnected = connected;

            if (connected) {
                connectionStatusText.textContent = `Connected to ${serverName}`;
                connectionDot.classList.add('connected');
                plexConnectionStatus.classList.remove('disconnected');
                plexConnectionStatus.classList.add('connected');
                checkAllLibrariesBtn.disabled = false;
                debugLibraryBtn.disabled = false;
            } else {
                connectionStatusText.textContent = 'Not connected to Plex server';
                connectionDot.classList.remove('connected');
                plexConnectionStatus.classList.remove('connected');
                plexConnectionStatus.classList.add('disconnected');
                checkAllLibrariesBtn.disabled = true;
                debugLibraryBtn.disabled = true;
            }
        }

        function loadPlaylistSuggestions() {
            suggestionsLoading.style.display = 'flex';
            playlistGrid.style.display = 'none';

            fetch('/playlists/suggestions')
                .then(response => response.json())
                .then(data => {
                    suggestionsLoading.style.display = 'none';

                    if (data.success) {
                        currentSuggestions = data.playlists;
                        displayPlaylistSuggestions(data.playlists);
                        playlistGrid.style.display = 'grid';
                        addConsoleMessage(`✅ Generated ${data.playlists.length} playlist suggestions`, 'success');
                    } else {
                        addConsoleMessage(`❌ Failed to load suggestions: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    suggestionsLoading.style.display = 'none';
                    addConsoleMessage(`❌ Error loading suggestions: ${error.message}`, 'error');
                });
        }

        function displayPlaylistSuggestions(playlists) {
            playlistGrid.innerHTML = '';

            playlists.forEach(playlist => {
                const card = createPlaylistCard(playlist);
                playlistGrid.appendChild(card);
            });
        }

        function createPlaylistCard(playlist) {
            const card = document.createElement('div');
            card.className = 'playlist-card';
            card.dataset.playlistId = playlist.id;

            const categoryClass = playlist.category || 'genre';
            const tags = playlist.tags.map(tag =>
                `<span class="playlist-tag ${categoryClass}">${tag}</span>`
            ).join('');

            const trackPreview = playlist.tracks.slice(0, 5).map(track =>
                `<div class="track-preview-item">
                    <div class="track-preview-title">${track.title}</div>
                    <div class="track-preview-artist">${track.artist}</div>
                </div>`
            ).join('');

            const duration = Math.floor(playlist.estimated_duration);
            const hours = Math.floor(duration / 60);
            const minutes = duration % 60;
            const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

            card.innerHTML = `
                <div class="playlist-status status-ready" id="status-${playlist.id}">Ready</div>

                <div class="playlist-header">
                    <div class="playlist-title">${playlist.title}</div>
                    <div class="playlist-description">${playlist.description}</div>
                </div>

                <div class="playlist-tags">
                    ${tags}
                </div>

                <div class="playlist-info">
                    <div class="track-count">${playlist.tracks.length} tracks</div>
                    <div class="duration">~${durationText}</div>
                </div>

                <div class="track-preview">
                    ${trackPreview}
                    ${playlist.tracks.length > 5 ? `<div class="track-preview-item" style="text-align: center; color: #999;">...and ${playlist.tracks.length - 5} more tracks</div>` : ''}
                </div>

                <div id="missing-info-${playlist.id}" class="missing-tracks-info" style="display: none;">
                    <div class="missing-count">Checking library...</div>
                </div>

                <div class="playlist-actions">
                    <button class="download-btn" onclick="downloadPlaylist('${playlist.id}')" id="download-${playlist.id}">
                        📥 Download & Create Playlist
                    </button>
                    <button class="preview-btn" onclick="toggleTrackPreview('${playlist.id}')">
                        👁️ Preview
                    </button>
                </div>

                <div class="progress-bar" id="progress-${playlist.id}" style="display: none;">
                    <div class="progress-fill" id="progress-fill-${playlist.id}"></div>
                </div>
            `;

            return card;
        }

        function checkPlaylistAgainstLibrary(playlistId) {
            const playlist = currentSuggestions.find(p => p.id === playlistId);
            if (!playlist || !plexConnected) return;

            const statusElement = document.getElementById(`status-${playlistId}`);
            const missingInfoElement = document.getElementById(`missing-info-${playlistId}`);
            const downloadBtn = document.getElementById(`download-${playlistId}`);

            statusElement.textContent = 'Checking...';
            statusElement.className = 'playlist-status status-checking';
            missingInfoElement.style.display = 'block';
            missingInfoElement.innerHTML = '<div class="missing-count">Checking against your library...</div>';

            fetch('/playlists/check_library', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tracks: playlist.tracks })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const missingCount = data.missing_count;
                    const existingCount = data.existing_count;

                    if (missingCount === 0) {
                        statusElement.textContent = 'All in Library';
                        statusElement.className = 'playlist-status status-completed';
                        missingInfoElement.className = 'missing-tracks-info no-missing';
                        missingInfoElement.innerHTML = `
                            <div class="missing-count none">All ${existingCount} tracks already in your library</div>
                        `;
                        downloadBtn.textContent = '✅ Already Complete';
                        downloadBtn.disabled = true;
                    } else {
                        statusElement.textContent = 'Ready';
                        statusElement.className = 'playlist-status status-ready';
                        missingInfoElement.className = 'missing-tracks-info';
                        missingInfoElement.innerHTML = `
                            <div class="missing-count">${missingCount} new tracks to download</div>
                            <div style="font-size: 0.8em; color: #CCCCCC; margin-top: 5px;">
                                ${existingCount} tracks already in library
                            </div>
                        `;
                        downloadBtn.textContent = `📥 Download ${missingCount} Tracks`;
                        downloadBtn.disabled = false;

                        // Store missing tracks for download
                        playlist.missingTracks = data.missing_tracks;
                    }
                } else {
                    statusElement.textContent = 'Check Failed';
                    statusElement.className = 'playlist-status status-error';
                    missingInfoElement.innerHTML = `<div class="missing-count none">Error: ${data.error}</div>`;
                }
            })
            .catch(error => {
                statusElement.textContent = 'Check Failed';
                statusElement.className = 'playlist-status status-error';
                missingInfoElement.innerHTML = `<div class="missing-count none">Error checking library</div>`;
                console.error('Library check error:', error);
            });
        }

        function checkAllPlaylistsAgainstLibrary() {
            if (!plexConnected) {
                addConsoleMessage('❌ Please connect to Plex server first', 'error');
                return;
            }

            addConsoleMessage('🔍 Checking all playlists against your Plex library...', 'info');
            currentSuggestions.forEach(playlist => {
                setTimeout(() => checkPlaylistAgainstLibrary(playlist.id), Math.random() * 2000);
            });
        }

        function downloadPlaylist(playlistId) {
            const playlist = currentSuggestions.find(p => p.id === playlistId);
            if (!playlist) return;

            if (!plexConnected) {
                addConsoleMessage('❌ Please connect to Plex server first', 'error');
                return;
            }

            // Use missing tracks if available, otherwise use all tracks
            const tracksToDownload = playlist.missingTracks || playlist.tracks;

            if (tracksToDownload.length === 0) {
                addConsoleMessage('❌ No tracks to download', 'warning');
                return;
            }

            // Confirmation dialog
            const confirmMessage = `Download ${tracksToDownload.length} tracks and create playlist "${playlist.title}"?`;
            if (!confirm(confirmMessage)) {
                return;
            }

            const statusElement = document.getElementById(`status-${playlistId}`);
            const downloadBtn = document.getElementById(`download-${playlistId}`);
            const progressBar = document.getElementById(`progress-${playlistId}`);
            const progressFill = document.getElementById(`progress-fill-${playlistId}`);

            statusElement.textContent = 'Starting...';
            statusElement.className = 'playlist-status status-downloading';
            downloadBtn.disabled = true;
            downloadBtn.innerHTML = '<span class="loading"></span>Starting...';
            progressBar.style.display = 'block';
            progressFill.style.width = '0%';

            fetch('/playlists/download_and_create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    playlist_name: playlist.title,
                    tracks: tracksToDownload
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addConsoleMessage(`✅ ${data.message}`, 'success');
                } else {
                    addConsoleMessage(`❌ Failed to start download: ${data.error}`, 'error');
                    resetPlaylistDownloadState(playlistId);
                }
            })
            .catch(error => {
                addConsoleMessage(`❌ Error starting download: ${error.message}`, 'error');
                resetPlaylistDownloadState(playlistId);
            });
        }

        function resetPlaylistDownloadState(playlistId) {
            const statusElement = document.getElementById(`status-${playlistId}`);
            const downloadBtn = document.getElementById(`download-${playlistId}`);
            const progressBar = document.getElementById(`progress-${playlistId}`);

            statusElement.textContent = 'Ready';
            statusElement.className = 'playlist-status status-ready';
            downloadBtn.disabled = false;
            downloadBtn.textContent = '📥 Download & Create Playlist';
            progressBar.style.display = 'none';
        }

        function toggleTrackPreview(playlistId) {
            const playlist = currentSuggestions.find(p => p.id === playlistId);
            if (!playlist) return;

            // Create a modal or expanded view for full track listing
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.8); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: #3A3A3A; border-radius: 15px; padding: 30px;
                max-width: 600px; max-height: 80vh; overflow-y: auto;
                border: 1px solid #4A4A4A;
            `;

            const trackList = playlist.tracks.map((track, index) =>
                `<div style="padding: 8px 0; border-bottom: 1px solid #555; display: flex; justify-content: space-between;">
                    <div>
                        <div style="color: #FFF; font-weight: bold;">${index + 1}. ${track.title}</div>
                        <div style="color: #CCC; font-size: 0.9em;">${track.artist} • ${track.album}</div>
                    </div>
                </div>`
            ).join('');

            content.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="color: #00BCD4; margin: 0;">${playlist.title}</h3>
                    <button onclick="this.closest('.modal').remove()" style="background: #F44336; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                </div>
                <div style="margin-bottom: 20px; color: #CCC;">${playlist.description}</div>
                <div style="max-height: 400px; overflow-y: auto;">
                    ${trackList}
                </div>
            `;

            modal.className = 'modal';
            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.addEventListener('click', (e) => {
                if (e.target === modal) modal.remove();
            });
        }

        function debugPlexLibrary() {
            if (!plexConnected) {
                addConsoleMessage('❌ Please connect to Plex server first', 'error');
                return;
            }

            debugLibraryBtn.disabled = true;
            debugLibraryBtn.innerHTML = '<span class="loading"></span>Debugging...';

            addConsoleMessage('🔍 Analyzing Plex library structure...', 'info');

            fetch('/plex/debug_library')
                .then(response => response.json())
                .then(data => {
                    debugLibraryBtn.disabled = false;
                    debugLibraryBtn.textContent = '🔍 Debug Library';

                    if (data.success) {
                        addConsoleMessage('✅ Debug information logged to server console', 'success');
                    } else {
                        addConsoleMessage(`❌ Debug failed: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    debugLibraryBtn.disabled = false;
                    debugLibraryBtn.textContent = '🔍 Debug Library';
                    addConsoleMessage(`❌ Debug error: ${error.message}`, 'error');
                });
        }

        function testDownload() {
            const testQuery = prompt("Enter a song to test download (e.g., 'Christina Aguilera Genie in a Bottle'):");
            if (!testQuery) return;

            testDownloadBtn.disabled = true;
            testDownloadBtn.innerHTML = '<span class="loading"></span>Testing...';

            addConsoleMessage(`🧪 Testing download for: ${testQuery}`, 'info');

            fetch('/test_download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: testQuery })
            })
            .then(response => response.json())
            .then(data => {
                testDownloadBtn.disabled = false;
                testDownloadBtn.textContent = '🧪 Test Download';

                if (data.success) {
                    addConsoleMessage(`✅ ${data.message}`, 'success');
                } else {
                    addConsoleMessage(`❌ Test failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                testDownloadBtn.disabled = false;
                testDownloadBtn.textContent = '🧪 Test Download';
                addConsoleMessage(`❌ Test error: ${error.message}`, 'error');
            });
        }

        // Socket event handlers for playlist progress
        socket.on('playlist_download_started', (data) => {
            addConsoleMessage(`🎵 ${data.message}`, 'info');
        });

        socket.on('playlist_progress', (data) => {
            const playlistCard = document.querySelector(`[data-playlist-id="${data.playlist_name.toLowerCase().replace(/\s+/g, '_')}"]`);
            if (playlistCard) {
                const statusElement = playlistCard.querySelector('.playlist-status');
                const downloadBtn = playlistCard.querySelector('.download-btn');
                const progressFill = playlistCard.querySelector('.progress-fill');

                if (data.status === 'downloading') {
                    statusElement.textContent = `${data.progress}/${data.total}`;
                    statusElement.className = 'playlist-status status-downloading';
                    downloadBtn.innerHTML = `<span class="loading"></span>Downloading...`;

                    if (progressFill) {
                        const percentage = (data.progress / data.total) * 100;
                        progressFill.style.width = `${percentage}%`;
                    }

                    if (data.current_track) {
                        addConsoleMessage(`⬇️ ${data.current_track}`, 'info');
                    }
                } else if (data.status === 'scanning_library') {
                    statusElement.textContent = 'Scanning...';
                    statusElement.className = 'playlist-status status-downloading';
                    downloadBtn.innerHTML = '<span class="loading"></span>Scanning Library...';
                    addConsoleMessage(`🔍 ${data.message}`, 'info');
                } else if (data.status === 'finding_tracks') {
                    statusElement.textContent = 'Finding...';
                    statusElement.className = 'playlist-status status-downloading';
                    downloadBtn.innerHTML = '<span class="loading"></span>Finding Tracks...';
                    addConsoleMessage(`🎵 ${data.message}`, 'info');
                } else if (data.status === 'creating_playlist') {
                    statusElement.textContent = 'Creating...';
                    statusElement.className = 'playlist-status status-downloading';
                    downloadBtn.innerHTML = '<span class="loading"></span>Creating Playlist...';
                    addConsoleMessage(`🎵 ${data.message}`, 'info');
                } else if (data.status === 'retrying') {
                    statusElement.textContent = 'Retrying...';
                    statusElement.className = 'playlist-status status-downloading';
                    downloadBtn.innerHTML = '<span class="loading"></span>Retrying...';
                    addConsoleMessage(`🔄 ${data.message}`, 'warning');
                }
            }
        });

        socket.on('playlist_completed', (data) => {
            const playlistCard = document.querySelector(`[data-playlist-id="${data.playlist_name.toLowerCase().replace(/\s+/g, '_')}"]`);
            if (playlistCard) {
                const statusElement = playlistCard.querySelector('.playlist-status');
                const downloadBtn = playlistCard.querySelector('.download-btn');
                const progressBar = playlistCard.querySelector('.progress-bar');
                const missingInfoElement = playlistCard.querySelector('.missing-tracks-info');

                if (data.success) {
                    statusElement.textContent = 'Completed';
                    statusElement.className = 'playlist-status status-completed';
                    downloadBtn.textContent = '✅ Completed';
                    downloadBtn.disabled = true;
                    progressBar.style.display = 'none';

                    // Update missing tracks info with completion details
                    if (missingInfoElement && data.found_count !== undefined) {
                        missingInfoElement.className = 'missing-tracks-info';
                        missingInfoElement.innerHTML = `
                            <div class="missing-count">✅ Playlist created successfully!</div>
                            <div style="font-size: 0.8em; color: #CCCCCC; margin-top: 5px;">
                                Downloaded: ${data.downloaded_count || 0} • Found: ${data.found_count} • Failed: ${data.failed_count || 0}
                            </div>
                        `;
                    }
                } else {
                    statusElement.textContent = 'Failed';
                    statusElement.className = 'playlist-status status-error';
                    downloadBtn.textContent = '❌ Retry';
                    downloadBtn.disabled = false;
                    progressBar.style.display = 'none';

                    // Update missing tracks info with failure details
                    if (missingInfoElement) {
                        missingInfoElement.className = 'missing-tracks-info no-missing';
                        let failureDetails = '';
                        if (data.downloaded_count !== undefined) {
                            failureDetails = `
                                <div style="font-size: 0.8em; color: #CCCCCC; margin-top: 5px;">
                                    Downloaded: ${data.downloaded_count} • Found: ${data.found_count || 0} • Failed: ${data.failed_count || 0}
                                </div>
                            `;
                        }
                        missingInfoElement.innerHTML = `
                            <div class="missing-count none">❌ Playlist creation failed</div>
                            ${failureDetails}
                        `;
                    }
                }
            }

            addConsoleMessage(data.message, data.success ? 'success' : 'error');
        });





        // Button event handlers
        startBtn.addEventListener('click', () => {
            if (!isRunning) {
                socket.emit('start_download');
                addConsoleMessage('Requesting download start...', 'info');
            }
        });

        stopBtn.addEventListener('click', () => {
            if (isRunning) {
                socket.emit('stop_download');
                addConsoleMessage('Requesting download stop...', 'warning');
            }
        });

        searchBtn.addEventListener('click', performSearch);

        // Enter key support for search inputs
        artistInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        albumInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        trackInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') performSearch();
        });

        // AI Playlist Suggestions event listeners
        refreshSuggestionsBtn.addEventListener('click', loadPlaylistSuggestions);
        checkAllLibrariesBtn.addEventListener('click', checkAllPlaylistsAgainstLibrary);
        debugLibraryBtn.addEventListener('click', debugPlexLibrary);
        testDownloadBtn.addEventListener('click', testDownload);

        // Initial status check
        fetch('/status')
            .then(response => response.json())
            .then(data => {
                updateStatus(data.running, data.running ? 'Download in progress...' : 'Ready to start download');
            })
            .catch(error => {
                addConsoleMessage('Failed to get initial status', 'error');
            });

        // Check Plex connection and load suggestions on page load
        checkPlexConnection();

        // Load initial playlist suggestions
        loadPlaylistSuggestions();
    </script>
</body>
</html>
